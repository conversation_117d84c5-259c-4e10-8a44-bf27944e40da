<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Garage The Mandi House – Our Menu</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="styles.css"/>
</head>
<body>

  <!-- Elegant Navbar -->
<nav class="elegant-navbar">
  <div class="nav-container">
    <div class="nav-logo">
      <span class="logo-text">Garage</span>
      <span class="logo-subtitle">The Mandi House</span>
    </div>
    <div class="nav-links">
      <a href="index.html" class="nav-link">Home</a>
      <a href="menu.html" class="nav-link active">Menu</a>
      <a href="gallery.html" class="nav-link">Gallery</a>
      <a href="location.html" class="nav-link">Location</a>
      <a href="contact.html" class="nav-link">Contact</a>
      <a href="reviews.html" class="nav-link">Reviews</a>
    </div>
  </div>
</nav>

<!-- Menu Hero Section -->
<section class="menu-hero">
  <div class="section-container">
    <div class="menu-hero-content">
      <h1 class="menu-title">Our Authentic Menu</h1>
      <p class="menu-subtitle">Discover the finest Arabian cuisine crafted with passion and precision</p>
    </div>
  </div>
</section>

<!-- Menu Section -->
<section id="menu">
  <h2>Full Menu</h2>

  <div class="menu-category">
    <button class="menu-toggle" onclick="toggleMenu(this)">Starters</button>
    <ul class="menu-items">
      <li><span>Paneer Tikka</span><span>₹180</span></li>
      <li><span>Chicken 65</span><span>₹160</span></li>
      <li><span>Hariyali Tikka</span><span>₹180</span></li>
      <li><span>Tandoori Tikka</span><span>₹180</span></li>
      <li><span>Tandoori Legs</span><span>₹180</span></li>
      <li><span>Chicken Lollipop</span><span>₹320</span></li>
      <li><span>Fish</span><span>₹180</span></li>
      <li><span>Garage Spl. Starter</span><span>₹899</span></li>
    </ul>
  </div>

  <div class="menu-category">
    <button class="menu-toggle" onclick="toggleMenu(this)">Paneer Mandi</button>
    <ul class="menu-items">
      <li><span>Paneer Mandi (Half)</span><span>₹260</span></li>
      <li><span>Paneer Mandi (Full)</span><span>₹460</span></li>
    </ul>
  </div>

  <div class="menu-category">
    <button class="menu-toggle" onclick="toggleMenu(this)">Chicken Faham Mandi</button>
    <ul class="menu-items">
      <li><span>Chicken Faham Mandi (Half)</span><span>₹480</span></li>
      <li><span>Chicken Faham Mandi (Full)</span><span>₹880</span></li>
    </ul>
  </div>

  <div class="menu-category">
    <button class="menu-toggle" onclick="toggleMenu(this)">Chicken Tandoori Legs Mandi</button>
    <ul class="menu-items">
      <li><span>Chicken Tandoori Legs Mandi (Half)</span><span>₹460</span></li>
      <li><span>Chicken Tandoori Legs Mandi (Full)</span><span>₹860</span></li>
    </ul>
  </div>

  <div class="menu-category">
    <button class="menu-toggle" onclick="toggleMenu(this)">Chicken Masala Mandi</button>
    <ul class="menu-items">
      <li><span>Chicken Masala Mandi (Half)</span><span>₹480</span></li>
      <li><span>Chicken Masala Mandi (Full)</span><span>₹880</span></li>
    </ul>
  </div>

  <div class="menu-category">
    <button class="menu-toggle" onclick="toggleMenu(this)">Chicken Fry Mandi</button>
    <ul class="menu-items">
      <li><span>Chicken Fry 1 pc Mandi</span><span>₹240</span></li>
      <li><span>Chicken Fry 2 pc Mandi</span><span>₹440</span></li>
      <li><span>Chicken Fry 3 pc Mandi</span><span>₹640</span></li>
      <li><span>Chicken Fry 4 pc Mandi</span><span>₹840</span></li>
    </ul>
  </div>

  <div class="menu-category">
    <button class="menu-toggle" onclick="toggleMenu(this)">Chicken 65 Mandi</button>
    <ul class="menu-items">
      <li><span>Chicken 65 Mandi (Half)</span><span>₹260</span></li>
      <li><span>Chicken 65 Mandi (Full)</span><span>₹460</span></li>
    </ul>
  </div>

  <div class="menu-category">
    <button class="menu-toggle" onclick="toggleMenu(this)">Chicken Broasted Mandi</button>
    <ul class="menu-items">
      <li><span>Chicken Broasted 1 pc Mandi</span><span>₹260</span></li>
      <li><span>Chicken Broasted 2 pc Mandi</span><span>₹460</span></li>
      <li><span>Chicken Broasted 3 pc Mandi</span><span>₹660</span></li>
      <li><span>Chicken Broasted 4 pc Mandi</span><span>₹860</span></li>
    </ul>
  </div>

  <div class="menu-category">
    <button class="menu-toggle" onclick="toggleMenu(this)">Chicken Juicy Mandi</button>
    <ul class="menu-items">
      <li><span>Chicken Juicy 1 pc Mandi</span><span>₹280</span></li>
      <li><span>Chicken Juicy 2 pc Mandi</span><span>₹480</span></li>
      <li><span>Chicken Juicy 3 pc Mandi</span><span>₹680</span></li>
      <li><span>Chicken Juicy 4 pc Mandi</span><span>₹880</span></li>
    </ul>
  </div>

  <div class="menu-category">
    <button class="menu-toggle" onclick="toggleMenu(this)">Mutton Ghee Roast Mandi</button>
    <ul class="menu-items">
      <li><span>Mutton Ghee Roast 1 pc Mandi</span><span>₹380</span></li>
      <li><span>Mutton Ghee Roast 2 pc Mandi</span><span>₹580</span></li>
    </ul>
  </div>

  <div class="menu-category">
    <button class="menu-toggle" onclick="toggleMenu(this)">Mutton Fry Mandi</button>
    <ul class="menu-items">
      <li><span>Mutton Fry 1 pc Mandi</span><span>₹330</span></li>
      <li><span>Mutton Fry 2 pc Mandi</span><span>₹530</span></li>
      <li><span>Mutton Fry 3 pc Mandi</span><span>₹730</span></li>
      <li><span>Mutton Fry 4 pc Mandi</span><span>₹930</span></li>
    </ul>
  </div>

  <div class="menu-category">
    <button class="menu-toggle" onclick="toggleMenu(this)">Mutton Juicy Mandi</button>
    <ul class="menu-items">
      <li><span>Mutton Juicy 1 pc Mandi</span><span>₹350</span></li>
      <li><span>Mutton Juicy 2 pc Mandi</span><span>₹550</span></li>
      <li><span>Mutton Juicy 3 pc Mandi</span><span>₹750</span></li>
      <li><span>Mutton Juicy 4 pc Mandi</span><span>₹950</span></li>
    </ul>
  </div>

  <div class="menu-category">
    <button class="menu-toggle" onclick="toggleMenu(this)">Fish & Prawns Mandi</button>
    <ul class="menu-items">
      <li><span>Fish 1 pc Mandi</span><span>₹400</span></li>
      <li><span>Fish 2 pc Mandi</span><span>₹750</span></li>
      <li><span>Prawns Mandi (Half)</span><span>₹400</span></li>
      <li><span>Prawns Mandi (Full)</span><span>₹700</span></li>
    </ul>
  </div>

  <div class="menu-category">
    <button class="menu-toggle" onclick="toggleMenu(this)">Garage Special Mandi</button>
    <ul class="menu-items">
      <li><span>Mini Garage Special Mandi</span><span>₹999</span></li>
      <li><span>Garage Special Mandi</span><span>₹1999</span></li>
    </ul>
  </div>

  <div class="menu-category">
    <button class="menu-toggle" onclick="toggleMenu(this)">Mix-in's</button>
    <ul class="menu-items">
      <li><span>Extra Mandi Rice</span><span>₹150</span></li>
      <li><span>Extra Chicken Fry Piece</span><span>₹150</span></li>
      <li><span>Extra Chicken Broasted Piece</span><span>₹170</span></li>
      <li><span>Extra Chicken Juicy Piece</span><span>₹180</span></li>
      <li><span>Extra Mutton Fry Piece</span><span>₹180</span></li>
      <li><span>Extra Mutton Juicy Piece</span><span>₹220</span></li>
      <li><span>Extra Mutton Ghee Roast Piece</span><span>₹220</span></li>
      <li><span>Extra Fish Piece</span><span>₹250</span></li>
      <li><span>Extra Mayonnaise</span><span>₹30</span></li>
      <li><span>Extra Soup</span><span>₹20</span></li>
    </ul>
  </div>

  <div class="menu-category">
    <button class="menu-toggle" onclick="toggleMenu(this)">Desserts</button>
    <ul class="menu-items">
      <li><span>Kaddu Kheer</span><span>₹50</span></li>
      <li><span>Double Ka Meeta</span><span>₹50</span></li>
      <li><span>Gulab Jamun</span><span>₹50</span></li>
    </ul>
  </div>

  <div class="menu-category">
    <button class="menu-toggle" onclick="toggleMenu(this)">Drinks</button>
    <ul class="menu-items">
      <li><span>Goli Soda (Any Flavour)</span><span>₹60</span></li>
      <li><span>Water Bottle / Soft Drinks</span><span>as per MRP</span></li>
    </ul>


</section>





<!-- Order CTA Section -->
<section class="order-cta-section">
  <div class="section-container">
    <div class="cta-content">
      <h2>Ready to Order?</h2>
      <p>Experience the authentic taste of Arabian cuisine delivered fresh to your door</p>
      <div class="cta-buttons">
        <a href="https://www.swiggy.com/restaurants/garage-the-mandi-house-kharmanghat-hyderabad-252403" target="_blank" class="cta-button swiggy">
          <span>Order on Swiggy</span>
        </a>
        <a href="https://www.zomato.com/hyderabad/garage-the-mandi-house-l-b-nagar/order" target="_blank" class="cta-button zomato">
          <span>Order on Zomato</span>
        </a>
      </div>
    </div>
  </div>
</section>

<!-- Footer -->
<footer class="elegant-footer">
  <div class="footer-container">
    <div class="footer-content">
      <div class="footer-brand">
        <h3>Garage The Mandi House</h3>
        <p>Authentic Arabian cuisine with a modern twist</p>
      </div>

      <div class="footer-links">
        <div class="link-group">
          <h4>Quick Links</h4>
          <a href="index.html">Home</a>
          <a href="gallery.html">Gallery</a>
          <a href="location.html">Location</a>
          <a href="contact.html">Contact</a>
        </div>

        <div class="link-group">
          <h4>Order Online</h4>
          <a href="https://www.swiggy.com/restaurants/garage-the-mandi-house-kharmanghat-hyderabad-252403" target="_blank">Swiggy</a>
          <a href="https://www.zomato.com/hyderabad/garage-the-mandi-house-l-b-nagar/order" target="_blank">Zomato</a>
        </div>
      </div>
    </div>

    <div class="footer-bottom">
      <p>© 2024 Garage The Mandi House. Made with ❤️ in Hyderabad</p>
    </div>
  </div>
</footer>

<script>
function toggleMenu(button) {
  console.log('Toggle menu called for:', button.textContent);
  const menuItems = button.nextElementSibling;
  console.log('Menu items element:', menuItems);
  console.log('Menu items tag name:', menuItems ? menuItems.tagName : 'null');
  console.log('Menu items class:', menuItems ? menuItems.className : 'null');

  if (menuItems) {
    console.log('Current display style:', menuItems.style.display);
    console.log('Current computed style:', window.getComputedStyle(menuItems).display);

    if (menuItems.style.display === 'block') {
      menuItems.style.display = 'none';
      console.log('Hiding menu');
    } else {
      menuItems.style.display = 'block';
      menuItems.style.visibility = 'visible';
      menuItems.style.opacity = '1';
      menuItems.style.height = 'auto';
      console.log('Showing menu');
    }

    console.log('After toggle - display:', menuItems.style.display);
    console.log('After toggle - computed:', window.getComputedStyle(menuItems).display);
  } else {
    console.log('ERROR: No menu items found!');
  }
}
</script>
<script src="script.js"></script>
</body>
</html>
