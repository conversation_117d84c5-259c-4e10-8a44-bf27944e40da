/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #2c2c2c;
  background: #fefefe;
  overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Playfair Display', serif;
  font-weight: 600;
  line-height: 1.2;
}

.section-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* Elegant Navbar */
.elegant-navbar {
  background: #ffffff;
  padding: 20px 0;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-logo {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.logo-text {
  font-family: 'Playfair Display', serif;
  font-size: 28px;
  font-weight: 700;
  color: #2c2c2c;
  line-height: 1;
}

.logo-subtitle {
  font-family: 'Inter', sans-serif;
  font-size: 12px;
  font-weight: 400;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-top: 2px;
}

.nav-links {
  display: flex;
  gap: 32px;
  align-items: center;
}

.nav-link {
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #2c2c2c;
  text-decoration: none;
  transition: color 0.3s ease;
  position: relative;
}

.nav-link:hover,
.nav-link.active {
  color: #8B4513;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background: #8B4513;
  transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

/* Hero Section */
.hero-main {
  background: #f8f6f0;
  min-height: 90vh;
  display: flex;
  align-items: center;
  padding: 60px 0;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.hero-content {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.hero-title {
  font-family: 'Playfair Display', serif;
  font-size: 48px;
  font-weight: 400;
  color: #2c2c2c;
  line-height: 1.2;
  margin: 0;
}

.subtitle {
  font-weight: 600;
  color: #8B4513;
  font-size: 32px;
  white-space: nowrap;
}

.hero-description {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.description-text {
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.rating-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.stars {
  color: #FFD700;
  font-size: 18px;
}

.rating-text {
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  color: #666;
}

/* Contact Form */
.contact-info h3 {
  font-family: 'Playfair Display', serif;
  font-size: 24px;
  color: #2c2c2c;
  margin-bottom: 24px;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #2c2c2c;
}

.form-group input {
  padding: 12px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  background: #ffffff;
  transition: border-color 0.3s ease;
}

.form-group input:focus {
  outline: none;
  border-color: #8B4513;
  box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.1);
}

.submit-btn {
  background: #8B4513;
  color: white;
  border: none;
  padding: 14px 24px;
  border-radius: 8px;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.submit-btn:hover {
  background: #6B3410;
}

/* Hero Image */
.hero-image {
  position: relative;
}

.image-container {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.main-image {
  width: 100%;
  height: auto;
  object-fit: contain;
  display: block;
  max-height: 600px;
}

.image-overlay {
  position: absolute;
  top: 24px;
  right: 24px;
}

.delivery-badge {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  max-width: 200px;
}

.badge-icon {
  font-size: 24px;
  margin-bottom: 12px;
}

.badge-content h4 {
  font-family: 'Playfair Display', serif;
  font-size: 18px;
  color: #2c2c2c;
  margin-bottom: 16px;
  line-height: 1.3;
}

.order-btn {
  background: #8B4513;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-family: 'Inter', sans-serif;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.order-btn:hover {
  background: #6B3410;
}

/* Features Section */
.features-section {
  padding: 80px 0;
  background: #ffffff;
}

.section-title {
  font-family: 'Playfair Display', serif;
  font-size: 36px;
  font-weight: 600;
  color: #2c2c2c;
  text-align: center;
  margin-bottom: 60px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
}

.feature-card {
  text-align: center;
  padding: 40px 24px;
  background: #f8f6f0;
  border-radius: 12px;
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-4px);
}

.feature-icon {
  font-size: 48px;
  margin-bottom: 20px;
  display: block;
}

.feature-card h3 {
  font-family: 'Playfair Display', serif;
  font-size: 24px;
  color: #2c2c2c;
  margin-bottom: 12px;
}

.feature-card p {
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  color: #666;
  line-height: 1.6;
}

/* Navigation Section */
.navigation-section {
  padding: 80px 0;
  background: #f8f6f0;
}

.nav-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 32px;
}

.nav-card {
  background: #ffffff;
  padding: 32px 24px;
  border-radius: 12px;
  text-decoration: none;
  text-align: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.nav-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.nav-icon {
  font-size: 40px;
  margin-bottom: 16px;
  display: block;
}

.nav-card h3 {
  font-family: 'Playfair Display', serif;
  font-size: 20px;
  color: #2c2c2c;
  margin-bottom: 8px;
}

.nav-card p {
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  color: #666;
  margin: 0;
}

/* Footer */
.elegant-footer {
  background: #2c2c2c;
  color: #ffffff;
  padding: 60px 0 30px;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  margin-bottom: 40px;
}

.footer-brand h3 {
  font-family: 'Playfair Display', serif;
  font-size: 24px;
  color: #ffffff;
  margin-bottom: 12px;
}

.footer-brand p {
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  color: #cccccc;
  margin: 0;
}

.footer-links {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
}

.link-group h4 {
  font-family: 'Playfair Display', serif;
  font-size: 18px;
  color: #ffffff;
  margin-bottom: 16px;
}

.link-group a {
  display: block;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  color: #cccccc;
  text-decoration: none;
  margin-bottom: 8px;
  transition: color 0.3s ease;
}

.link-group a:hover {
  color: #8B4513;
}

.footer-bottom {
  border-top: 1px solid #444;
  padding-top: 30px;
  text-align: center;
}

.footer-bottom p {
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  color: #cccccc;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-container {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .nav-links {
    gap: 20px;
  }

  .hero-container {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .hero-title {
    font-size: 36px;
  }

  .main-image {
    max-height: 400px;
    height: auto;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .nav-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .footer-links {
    grid-template-columns: 1fr;
    gap: 30px;
  }
}

@media (max-width: 480px) {
  .section-container {
    padding: 0 16px;
  }

  .hero-title {
    font-size: 28px;
  }

  .section-title {
    font-size: 28px;
  }

  .nav-grid {
    grid-template-columns: 1fr;
  }

  .delivery-badge {
    position: static;
    margin-top: 20px;
    max-width: none;
  }
}

/* Menu Page Styles */
.menu-hero {
  background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
  color: white;
  padding: 80px 0;
  text-align: center;
}

.menu-hero-content {
  max-width: 600px;
  margin: 0 auto;
}

.menu-title {
  font-family: 'Playfair Display', serif;
  font-size: 48px;
  font-weight: 600;
  margin-bottom: 16px;
}

.menu-subtitle {
  font-family: 'Inter', sans-serif;
  font-size: 18px;
  opacity: 0.9;
  margin: 0;
}

.menu-section {
  padding: 80px 0;
  background: #f8f6f0;
}

.menu-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 32px;
}

.menu-category-card {
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
}

.menu-category-card:hover {
  transform: translateY(-4px);
}

.menu-category-card.featured {
  border: 2px solid #8B4513;
}

.category-header {
  background: #2c2c2c;
  color: white;
  padding: 20px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.menu-category-card.featured .category-header {
  background: #8B4513;
}

.category-title {
  font-family: 'Playfair Display', serif;
  font-size: 20px;
  margin: 0;
}

.menu-toggle {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0;
}

.toggle-icon {
  font-size: 24px;
  font-weight: 300;
  transition: transform 0.3s ease;
}

.menu-items {
  padding: 0;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.menu-category-card.active .menu-items {
  max-height: 1000px;
  padding: 24px;
}

.menu-category-card.active .toggle-icon {
  transform: rotate(45deg);
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item.special {
  background: #fff8f0;
  padding: 16px 12px;
  border-radius: 8px;
  border: 1px solid #8B4513;
}

.item-name {
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  color: #2c2c2c;
  font-weight: 500;
}

.item-price {
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  color: #8B4513;
  font-weight: 600;
}

/* Order CTA Section */
.order-cta-section {
  background: #2c2c2c;
  color: white;
  padding: 60px 0;
  text-align: center;
}

.cta-content h2 {
  font-family: 'Playfair Display', serif;
  font-size: 36px;
  margin-bottom: 16px;
}

.cta-content p {
  font-family: 'Inter', sans-serif;
  font-size: 18px;
  margin-bottom: 32px;
  opacity: 0.9;
}

.cta-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-button {
  background: #8B4513;
  color: white;
  padding: 16px 32px;
  border-radius: 8px;
  text-decoration: none;
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  transition: background-color 0.3s ease;
}

.cta-button:hover {
  background: #6B3410;
}

.cta-button.zomato {
  background: #E23744;
}

.cta-button.zomato:hover {
  background: #C8202C;
}

/* Gallery Page Styles */
.gallery-hero {
  background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
  color: white;
  padding: 80px 0;
  text-align: center;
}

.gallery-hero-content {
  max-width: 600px;
  margin: 0 auto;
}

.gallery-title {
  font-family: 'Playfair Display', serif;
  font-size: 48px;
  font-weight: 600;
  margin-bottom: 16px;
}

.gallery-section {
  padding: 80px 0;
  background: #f8f6f0;
}

.gallery-categories {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 60px;
  flex-wrap: wrap;
}

.category-filter {
  background: #ffffff;
  border: 2px solid #e0e0e0;
  color: #2c2c2c;
  padding: 12px 24px;
  border-radius: 25px;
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.category-filter:hover,
.category-filter.active {
  background: #8B4513;
  color: white;
  border-color: #8B4513;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
}

.gallery-item {
  transition: opacity 0.3s ease;
}

.gallery-item.hidden {
  opacity: 0;
  pointer-events: none;
}

.gallery-card {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.gallery-card:hover {
  transform: translateY(-8px);
}

.gallery-image {
  width: 100%;
  height: 300px;
  object-fit: cover;
  display: block;
}

.gallery-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 32px 24px 24px;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.gallery-card:hover .gallery-overlay {
  transform: translateY(0);
}

.gallery-overlay h3 {
  font-family: 'Playfair Display', serif;
  font-size: 20px;
  margin-bottom: 8px;
}

.gallery-overlay p {
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  opacity: 0.9;
  margin: 0;
}

/* Instagram Section */
.instagram-section {
  background: #ffffff;
  padding: 80px 0;
  text-align: center;
}

.instagram-content {
  max-width: 600px;
  margin: 0 auto;
}

.instagram-subtitle {
  font-family: 'Inter', sans-serif;
  font-size: 18px;
  color: #666;
  margin-bottom: 40px;
}

.instagram-accounts {
  display: flex;
  justify-content: center;
  gap: 32px;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.instagram-account {
  display: flex;
  align-items: center;
  gap: 12px;
  background: #f8f6f0;
  padding: 16px 24px;
  border-radius: 12px;
  text-decoration: none;
  color: #2c2c2c;
  transition: transform 0.3s ease;
}

.instagram-account:hover {
  transform: translateY(-2px);
}

.account-icon {
  font-size: 24px;
}

.instagram-follow-btn {
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  color: white;
  padding: 16px 32px;
  border-radius: 8px;
  text-decoration: none;
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  transition: transform 0.3s ease;
  display: inline-block;
}

.instagram-follow-btn:hover {
  transform: translateY(-2px);
}

/* Contact Page Styles */
.contact-hero {
  background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
  color: white;
  padding: 80px 0;
  text-align: center;
}

.contact-hero-content {
  max-width: 600px;
  margin: 0 auto;
}

.contact-title {
  font-family: 'Playfair Display', serif;
  font-size: 48px;
  font-weight: 600;
  margin-bottom: 16px;
}

.contact-subtitle {
  font-family: 'Inter', sans-serif;
  font-size: 18px;
  opacity: 0.9;
  margin: 0;
}

.contact-section {
  padding: 80px 0;
  background: #f8f6f0;
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: start;
}

.contact-info-card {
  background: #ffffff;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.contact-info-card h2 {
  font-family: 'Playfair Display', serif;
  font-size: 28px;
  color: #2c2c2c;
  margin-bottom: 32px;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 32px;
}

.contact-item:last-child {
  margin-bottom: 0;
}

.contact-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  background: #8B4513;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.contact-details h3 {
  font-family: 'Playfair Display', serif;
  font-size: 18px;
  color: #2c2c2c;
  margin-bottom: 4px;
}

.contact-details p {
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  color: #666;
  margin: 0;
  line-height: 1.5;
}

.reservation-card {
  background: #ffffff;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.reservation-card h2 {
  font-family: 'Playfair Display', serif;
  font-size: 28px;
  color: #2c2c2c;
  margin-bottom: 32px;
}

.elegant-reservation-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #2c2c2c;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 12px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  background: #ffffff;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #8B4513;
  box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.reservation-btn {
  background: #8B4513;
  color: white;
  border: none;
  padding: 16px 24px;
  border-radius: 8px;
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.reservation-btn:hover {
  background: #6B3410;
}

.reservation-note {
  margin-top: 32px;
  padding: 24px;
  background: #f8f6f0;
  border-radius: 8px;
  border-left: 4px solid #8B4513;
}

.reservation-note h3 {
  font-family: 'Playfair Display', serif;
  font-size: 18px;
  color: #2c2c2c;
  margin-bottom: 12px;
}

.reservation-note p {
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.reservation-note p:last-child {
  margin-bottom: 0;
}

.phone-link {
  color: #8B4513;
  text-decoration: none;
  font-weight: 600;
}

.phone-link:hover {
  text-decoration: underline;
}

/* Contact Page Responsive */
@media (max-width: 768px) {
  .contact-grid {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .contact-info-card,
  .reservation-card {
    padding: 24px;
  }
}

/* Location Page Styles */
.location-hero {
  background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
  color: white;
  padding: 80px 0;
  text-align: center;
}

.location-hero-content {
  max-width: 600px;
  margin: 0 auto;
}

.location-title {
  font-family: 'Playfair Display', serif;
  font-size: 48px;
  font-weight: 600;
  margin-bottom: 16px;
}

.location-section {
  padding: 80px 0;
  background: #f8f6f0;
}

.location-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: start;
}

.location-info-card {
  background: #ffffff;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.location-info-card h2 {
  font-family: 'Playfair Display', serif;
  font-size: 28px;
  color: #2c2c2c;
  margin-bottom: 32px;
}

.location-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 32px;
}

.location-item:last-child {
  margin-bottom: 0;
}

.location-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  background: #8B4513;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.location-details h3 {
  font-family: 'Playfair Display', serif;
  font-size: 18px;
  color: #2c2c2c;
  margin-bottom: 4px;
}

.location-details p {
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  color: #666;
  margin: 0;
  line-height: 1.5;
}

.directions-btn-container {
  margin-top: 32px;
}

.directions-btn {
  background: #8B4513;
  color: white;
  padding: 16px 32px;
  border-radius: 8px;
  text-decoration: none;
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  transition: background-color 0.3s ease;
  display: inline-block;
}

.directions-btn:hover {
  background: #6B3410;
}

.map-card {
  background: #ffffff;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.map-card h2 {
  font-family: 'Playfair Display', serif;
  font-size: 28px;
  color: #2c2c2c;
  margin-bottom: 24px;
}

.map-container {
  border-radius: 12px;
  overflow: hidden;
  height: 400px;
}

.map-iframe {
  width: 100%;
  height: 100%;
  border: none;
}

/* Reviews Page Styles */
.reviews-hero {
  background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
  color: white;
  padding: 80px 0;
  text-align: center;
}

.reviews-hero-content {
  max-width: 600px;
  margin: 0 auto;
}

.reviews-title {
  font-family: 'Playfair Display', serif;
  font-size: 48px;
  font-weight: 600;
  margin-bottom: 16px;
}

.overall-rating {
  margin-top: 32px;
}

.overall-rating .stars {
  color: #FFD700;
  font-size: 32px;
  margin-bottom: 8px;
}

.overall-rating .rating-text {
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  opacity: 0.9;
}

.reviews-section {
  padding: 80px 0;
  background: #f8f6f0;
}

.reviews-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 32px;
}

.review-card {
  background: #ffffff;
  padding: 32px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
}

.review-card:hover {
  transform: translateY(-4px);
}

.review-stars {
  color: #FFD700;
  font-size: 18px;
  margin-bottom: 16px;
}

.review-card blockquote {
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  color: #2c2c2c;
  line-height: 1.6;
  margin: 0 0 20px 0;
  font-style: italic;
}

.reviewer-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.reviewer-name {
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 600;
  color: #8B4513;
}

.review-date {
  font-family: 'Inter', sans-serif;
  font-size: 12px;
  color: #999;
}

/* Location and Reviews Responsive */
@media (max-width: 768px) {
  .location-grid {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .location-info-card,
  .map-card {
    padding: 24px;
  }

  .map-container {
    height: 300px;
  }

  .reviews-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .review-card {
    padding: 24px;
  }
}
.tool-item.gear2 { top: 15%; left: 60%; animation-delay: 6s; animation: spin 10s linear infinite reverse; }
.tool-item.hammer2 { top: 50%; right: 5%; animation-delay: 7s; }

/* Garage Door Effect */
.garage-door {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  display: flex;
  z-index: 1;
  pointer-events: none;
}

.door-panel {
  flex: 1;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(45, 45, 45, 0.1) 2%,
    transparent 4%);
  border-right: 1px solid rgba(255, 255, 255, 0.05);
}

.hero-overlay {
  position: relative;
  z-index: 3;
  max-width: 1200px;
  padding: 0 2rem;
}

.hero-content {
  color: white;
  text-align: center;
  z-index: 10;
}

/* Garage Sign with Neon Effect */
.garage-sign {
  margin-bottom: 2rem;
}

.neon-border {
  border: 3px solid #d35400;
  border-radius: 15px;
  padding: 1rem 2rem;
  background: rgba(0, 0, 0, 0.3);
  box-shadow:
    0 0 20px #d35400,
    inset 0 0 20px rgba(211, 84, 0, 0.1);
  animation: neonPulse 3s ease-in-out infinite;
}

.garage-title {
  margin: 0;
  font-family: 'Rajdhani', sans-serif;
  font-weight: 900;
  text-transform: uppercase;
  letter-spacing: 4px;
  line-height: 1.2;
}

.garage-text {
  display: block;
  font-size: 3.5rem;
  color: #d35400;
  text-shadow: 0 0 10px #d35400;
}

.the-text {
  display: block;
  font-size: 1.5rem;
  color: #f39c12;
  margin: 0.5rem 0;
}

.mandi-text {
  display: block;
  font-size: 2.5rem;
  color: #f0f0f0;
  text-shadow: 0 0 10px #f0f0f0;
}

/* Engine Status Display */
.engine-status {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2rem;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 25px;
  border: 1px solid #f39c12;
}

.status-light {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #f39c12;
  margin: 0 1rem;
  animation: blink 2s infinite;
  box-shadow: 0 0 10px #f39c12;
}

.status-text {
  font-family: 'Rajdhani', sans-serif;
  font-weight: 600;
  letter-spacing: 2px;
  color: #f0f0f0;
}

/* Advanced Tagline with Typing Effect */
.tagline-container {
  margin-bottom: 2rem;
}

.tagline {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #f0f0f0;
  font-weight: 400;
}

.tagline .line {
  display: block;
  margin-bottom: 0.5rem;
  opacity: 0;
  animation: fadeInUp 1s ease forwards;
}

.tagline .line:nth-child(1) { animation-delay: 0.5s; }
.tagline .line:nth-child(2) { animation-delay: 1s; }
.tagline .line:nth-child(3) { animation-delay: 1.5s; }
.tagline .line:nth-child(4) { animation-delay: 2s; }

/* Digital Display */
.digital-display {
  margin-bottom: 2rem;
}

.display-screen {
  background: #000;
  border: 2px solid #f39c12;
  border-radius: 10px;
  padding: 1rem;
  font-family: 'Courier New', monospace;
  color: #f39c12;
  text-align: center;
  box-shadow:
    0 0 20px rgba(243, 156, 18, 0.3),
    inset 0 0 10px rgba(243, 156, 18, 0.1);
}

.display-label {
  display: block;
  font-size: 0.8rem;
  margin-bottom: 0.5rem;
  opacity: 0.8;
}

.display-time {
  display: block;
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  text-shadow: 0 0 5px #f39c12;
}

.display-status {
  display: block;
  font-size: 0.9rem;
  color: #00ff00;
  animation: blink 3s infinite;
}

/* Enhanced Action Buttons */
.hero-actions {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.hero-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem 2rem;
  background: linear-gradient(45deg, #1a1a1a, #2d2d2d);
  color: white;
  text-decoration: none;
  border-radius: 15px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.4s ease;
  border: 2px solid #d35400;
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
  min-width: 180px;
}

.hero-btn::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #d35400, #f39c12, #d35400);
  border-radius: 17px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.hero-btn:hover::before {
  opacity: 1;
}

.hero-btn:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow:
    0 15px 35px rgba(211, 84, 0, 0.4),
    0 0 25px #f39c12;
}

.btn-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.btn-text {
  font-size: 0.9rem;
  margin-bottom: 0.3rem;
}

.btn-subtitle {
  font-size: 0.7rem;
  opacity: 0.8;
  font-weight: 400;
  text-transform: none;
}

.hero-btn.primary {
  border-color: #f39c12;
}

.hero-btn.swiggy {
  border-color: #fc8019;
}

.hero-btn.zomato {
  border-color: #e23744;
}

/* Garage Features */
.garage-features {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  border: 1px solid rgba(243, 156, 18, 0.3);
  min-width: 120px;
  transition: all 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-3px);
  border-color: #f39c12;
  box-shadow: 0 5px 15px rgba(243, 156, 18, 0.2);
}

.feature-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.feature-item span {
  font-size: 0.8rem;
  text-align: center;
  font-weight: 500;
}

/* Tire Tracks */
.tire-tracks {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50px;
  z-index: 2;
  pointer-events: none;
}

.track {
  position: absolute;
  bottom: 0;
  height: 4px;
  background: repeating-linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.1) 0px,
    rgba(255, 255, 255, 0.1) 10px,
    transparent 10px,
    transparent 20px
  );
}

.track-1 {
  left: 20%;
  width: 25%;
  bottom: 10px;
}

.track-2 {
  right: 20%;
  width: 25%;
  bottom: 20px;
}

/* Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(5deg); }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes neonPulse {
  0%, 100% { box-shadow: 0 0 20px #d35400, inset 0 0 20px rgba(211, 84, 0, 0.1); }
  50% { box-shadow: 0 0 30px #d35400, inset 0 0 30px rgba(211, 84, 0, 0.2); }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Gallery Section */
.gallery {
  padding: 3rem 2rem;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 1200px;
  margin: 3rem auto;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.gallery h2 {
  font-size: 2.2rem;
  color: #d35400;
  margin-bottom: 0.5rem;
}

.gallery-subtitle {
  font-size: 1rem;
  color: #666;
  margin-bottom: 2rem;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  max-width: 1100px;
  margin: 0 auto;
}

.gallery-item {
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  background-color: #fff;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 200px;
}

.gallery-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.gallery-item img {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: cover;
  transition: filter 0.3s;
}

.gallery-item:hover img {
  filter: brightness(1.05);
}

/* Lightbox Overlay */
#lightbox-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.85);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  display: none;
}

#lightbox-overlay img {
  max-width: 90%;
  max-height: 90%;
  border-radius: 10px;
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
  animation: fadeIn 0.4s ease;
}

/* Menu Section */
#menu {
  background-color: #fffefb;
  padding: 3rem 2rem;
  border-top: 4px solid #f39c12;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  max-width: 1000px;
  margin: 2rem auto;
}

#menu h2 {
  text-align: center;
  font-size: 3rem;
  margin-bottom: 2rem;
  color: #d35400;
  background: linear-gradient(90deg, #f39c12, #e67e22);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: fadeInDown 1s ease-out;
  text-transform: uppercase;
  font-weight: 800;
  letter-spacing: 2px;
}

.menu-category {
  margin-bottom: 1.5rem;
}

.menu-toggle {
  background: linear-gradient(90deg, #ffd9b3, #fff4e6);
  border: none;
  border-left: 8px solid #f39c12;
  border-radius: 4px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.05);
  color: #2c3e50;
  cursor: pointer;
  font-family: 'Poppins', sans-serif;
  font-size: 1.2rem;
  padding: 12px;
  text-align: left;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease-in-out;
  width: 100%;
}

.menu-toggle:hover {
  transform: scale(1.02);
  background: linear-gradient(90deg, #ffe0b3, #ffcc80);
  color: #d35400;
  border-left-color: #e67e22;
}

.menu-items {
  list-style: none;
  padding: 0;
  margin: 0;
  display: none;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

/* Show when display is set to block */
.menu-items[style*="display: block"] {
  display: block !important;
  padding: 15px;
  margin: 10px 0;
}

.menu-items li {
  padding: 15px 20px;
  font-size: 16px;
  font-family: 'Inter', sans-serif;
  border-bottom: 1px solid #e9ecef;
  display: flex !important;
  justify-content: space-between;
  align-items: center;
  transition: background 0.3s ease;
  color: #333;
  background: white;
}

.menu-items li:last-child {
  border-bottom: none;
}

.menu-items li:hover {
  background: #f8f9fa;
}

.menu-items li span:first-child {
  font-weight: 500;
  color: #2c2c2c;
  flex: 1;
}

.menu-items li span:last-child {
  font-weight: 600;
  color: #e67e22;
  font-size: 18px;
}

.menu-items li:hover {
  background: #fff8f0;
  cursor: pointer;
}
#location, #contact, #reviews {
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid #ddd;
}
#location h2, #contact h2, #reviews h2 {
    font-size: 2rem;
    color: #333;
    margin-bottom: 1.5rem;
}

.info-box {
  background-color: #fff;
  padding: 1.5rem;
  border-left: 5px solid #f39c12;
  border-radius: 8px;
  max-width: 700px;
  margin: 0 auto 2rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  text-align: left;
}

.info-box p {
  margin: 0.5rem 0;
  font-size: 1rem;
  color: #333;
}

.info-box a {
  color: #3498db;
  text-decoration: none;
  font-weight: bold;
}

.info-box a:hover {
  text-decoration: underline;
}

.map-container {
  max-width: 700px;
  margin: 0 auto;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.map-container iframe {
  width: 100%;
  height: 300px;
  border: none;
}

/* Remove redundant styling */

.reservation-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 2rem;
  max-width: 900px;
  margin: 0 auto;
}

.reservation-form {
  flex: 1 1 300px;
  background: #fff;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.reservation-form label {
  font-weight: bold;
  margin-bottom: 0.2rem;
}

.reservation-form input,
.reservation-form select {
  padding: 0.6rem;
  font-size: 1rem;
  border-radius: 5px;
  border: 1px solid #ccc;
}

.reservation-form button {
  background-color: #d35400;
  color: white;
  padding: 0.75rem;
  border: none;
  font-size: 1rem;
  border-radius: 5px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.reservation-form button:hover {
  background-color: #e67e22;
}

.reservation-info {
  flex: 1 1 250px;
  background: #fff8f0;
  padding: 1.5rem;
  border-left: 4px solid #f39c12;
  border-radius: 8px;
}

.reservation-info h3 {
  margin-top: 0;
  color: #2c3e50;
}

.reservation-info p {
  margin: 0.5rem 0;
}

/* Remove redundant styling */

.review-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1.5rem;
}

.review-box {
  background-color: #fff;
  border-left: 5px solid #f39c12;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
  padding: 1rem;
  max-width: 300px;
  text-align: left;
  border-radius: 8px;
}

.review-box blockquote {
  font-style: italic;
  margin: 0 0 0.5rem;
  color: #2c3e50;
}

.review-box p {
  margin: 0;
  font-weight: bold;
  color: #555;
}

/* Footer */
footer {
  background: rgba(0, 0, 0, 0.8);
  color: #f0f0f0;
  text-align: center;
  padding: 1rem;
  font-size: 0.9rem;
  margin-top: 2rem;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
footer a.instagram-link {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  font-weight: bold;
  color: #E1306C; /* Instagram pink */
  font-size: 16px;
  transition: transform 0.2s ease, color 0.2s ease;
}

footer a.instagram-link:hover {
  transform: scale(1.05);
  color: #C13584; /* Slightly darker on hover */
}

footer a.instagram-link img {
  width: 24px;
  height: 24px;
  border-radius: 5px;
}

/* Instagram Feed Section */
.instagram-feed {
  padding: 3rem 2rem;
  text-align: center;
  background: #fff;
}

.instagram-feed h2 {
  font-size: 2.5rem;
  color: #d35400;
  margin-bottom: 0.5rem;
}

.instagram-feed p {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 2rem;
}

.instagram-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.instagram-post {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 8px 16px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.instagram-post:hover {
  transform: translateY(-5px);
}

.instagram-post img {
  width: 100%;
  display: block;
  transition: transform 0.3s ease;
}

.instagram-post:hover img {
  transform: scale(1.05);
}

.instagram-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1.5rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.instagram-post:hover .instagram-overlay {
  opacity: 1;
}

.instagram-likes, .instagram-comments {
  font-size: 1.2rem;
  font-weight: bold;
}

.instagram-follow-btn {
  display: inline-block;
  margin-top: 2rem;
  padding: 0.8rem 2rem;
  background-color: #d35400;
  color: #fff;
  text-decoration: none;
  font-size: 1.1rem;
  border-radius: 30px;
  transition: background-color 0.3s ease;
}

.instagram-follow-btn:hover {
  background-color: #e67e22;
}

/* Instagram Reels Grid */
.embed-reels {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  max-width: 1200px;
  margin: 2rem auto 0;
  justify-items: center;
}

.instagram-media {
  background: #FFF;
  border: 0;
  border-radius: 12px;
  box-shadow: 0 8px 16px rgba(0,0,0,0.1);
  margin: 0 !important; /* Override default margin */
  max-width: 320px !important; /* Control max width */
  width: 100% !important; /* Ensure it fills the grid cell */
  min-width: 280px !important; /* Prevent it from becoming too small */
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.instagram-media:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 24px rgba(0,0,0,0.15);
}

/* Floating CTA Buttons */
.floating-cta {
  position: fixed;
  bottom: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 1001;
}

.floating-cta .cta-btn {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  border-radius: 25px;
  text-decoration: none;
  color: white;
  font-weight: 500;
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
  transition: transform 0.3s ease;
}

.floating-cta .cta-btn:hover {
  transform: scale(1.05);
}

.floating-cta .cta-btn img {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

.floating-cta .swiggy {
  background-color: #FC8019;
  text-align: center;
}

.floating-cta .zomato {
  background-color: #E23744;
  justify-content: center;
  text-align: center;
}

/* Media Queries for Responsive Design */

/* Tablets and smaller desktops */
@media (max-width: 1024px) {
  .gallery-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

/* Mobile devices */
@media (max-width: 768px) {
  .navbar {
    flex-direction: column;
    padding: 1rem;
  }

  .nav-links {
    margin-top: 1rem;
    gap: 1rem;
  }

  .hero-content h1 {
    font-size: 2.5rem;
  }

  .tagline {
    font-size: 1rem;
  }

  .gallery-grid {
    grid-template-columns: 1fr;
  }

  .reservation-container {
    flex-direction: column;
  }

  .review-container {
    flex-direction: column;
    align-items: center;
  }

  .embed-reels {
    grid-template-columns: 1fr;
  }

  .floating-cta {
    bottom: 10px;
    right: 10px;
  }
}

/* Smaller mobile devices */
@media (max-width: 480px) {
  .nav-logo {
    font-size: 1.2rem;
  }

  .nav-links a {
    font-size: 0.9rem;
  }

  .hero-content h1 {
    font-size: 2rem;
  }

  .tagline {
    font-size: 0.9rem;
  }

  .hero-btn {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
  }

  #menu h2 {
    font-size: 2rem;
  }

  .menu-toggle {
    font-size: 1rem;
  }

  .floating-cta .cta-btn {
    padding: 8px 12px;
  }

  .floating-cta .cta-btn span {
    display: none;
  }

  .floating-cta .cta-btn img {
    margin-right: 0;
  }
}

/* ========== QUICK NAVIGATION SECTION ========== */
.quick-nav {
  padding: 5rem 2rem;
  background:
    linear-gradient(135deg, var(--dark-steel) 0%, var(--steel-gray) 50%, var(--dark-steel) 100%),
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><rect width="100" height="100" fill="%23000"/><rect x="10" y="10" width="80" height="2" fill="%23333"/><rect x="10" y="88" width="80" height="2" fill="%23333"/></svg>');
  position: relative;
}

.quick-nav::before {
  content: '🔧 ⚙️ 🛠️ 🎯 📱 💻 🌐 🏁';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  font-size: 14px;
  opacity: 0.02;
  display: flex;
  align-items: center;
  justify-content: space-around;
  flex-wrap: wrap;
  padding: 2rem;
  pointer-events: none;
  z-index: 1;
}

.quick-nav-container {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.quick-nav h2 {
  text-align: center;
  font-family: 'Rajdhani', sans-serif;
  font-size: 2.8rem;
  color: #f39c12;
  margin-bottom: 3rem;
  text-transform: uppercase;
  letter-spacing: 2px;
  font-weight: 700;
  position: relative;
}

.quick-nav h2::before {
  content: '⚙️';
  position: absolute;
  left: -50px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 2rem;
  color: #d35400;
}

.quick-nav h2::after {
  content: '🔧';
  position: absolute;
  right: -50px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 2rem;
  color: #d35400;
}

.nav-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.nav-card {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border: 2px solid #d35400;
  border-radius: 15px;
  padding: 2rem;
  text-align: center;
  text-decoration: none;
  color: #f0f0f0;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.nav-card::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #d35400, #f39c12, #d35400);
  border-radius: 17px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.nav-card:hover::before {
  opacity: 1;
}

.nav-card:hover {
  transform: translateY(-10px) scale(1.05);
  box-shadow:
    0 20px 50px rgba(211, 84, 0, 0.6),
    0 0 30px rgba(243, 156, 18, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border-color: #f39c12;
}

.nav-card-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
}

.nav-card h3 {
  font-family: 'Rajdhani', sans-serif;
  font-size: 1.5rem;
  color: #f39c12;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 600;
}

.nav-card p {
  font-size: 0.9rem;
  color: #f0f0f0;
  line-height: 1.4;
  opacity: 0.8;
}