@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Rajdhani:wght@300;400;500;600;700&family=Bebas+Neue&display=swap');

/* ========== GARAGE RESTAURANT THEME ========== */

/* CSS Variables for Garage Theme */
:root {
  --primary-red: #dc143c;
  --accent-orange: #ff4500;
  --steel-gray: #2c2c2c;
  --matte-black: #0d0d0d;
  --chrome-silver: #c0c0c0;
  --neon-yellow: #ffff00;
  --dark-steel: #1a1a1a;
  --rust-orange: #cc5500;
  --metal-blue: #4a90e2;
  --garage-green: #32cd32;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body, html {
  font-family: 'Rajdhani', sans-serif;
  background: var(--matte-black);
  color: var(--chrome-silver);
  line-height: 1.6;
  overflow-x: hidden;
  position: relative;
  scroll-behavior: smooth;
}

/* Industrial Background Pattern */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 25% 25%, rgba(220, 20, 60, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 69, 0, 0.05) 0%, transparent 50%),
    linear-gradient(45deg, var(--matte-black) 25%, var(--dark-steel) 25%, var(--dark-steel) 50%, var(--matte-black) 50%, var(--matte-black) 75%, var(--dark-steel) 75%);
  background-size: 400px 400px, 400px 400px, 60px 60px;
  z-index: -2;
  opacity: 0.3;
}

/* Static Garage Tools Pattern */
body::after {
  content: '🔧 ⚙️ 🔩 🛠️ 🔨 ⚡ 🏁 🚗 🔧 ⚙️ 🔩 🛠️ 🔨 ⚡ 🏁 🚗';
  position: fixed;
  top: 0;
  left: 0;
  width: 120%;
  height: 120%;
  font-size: 16px;
  opacity: 0.02;
  z-index: -1;
  pointer-events: none;
  white-space: pre-wrap;
  word-spacing: 80px;
  line-height: 120px;
  padding: 50px;
  transform: rotate(-15deg);
}

/* Custom Scrollbar - Chrome Style */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: var(--matte-black);
  border-left: 2px solid var(--primary-red);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, var(--primary-red) 0%, var(--accent-orange) 100%);
  border-radius: 6px;
  border: 1px solid var(--steel-gray);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, var(--accent-orange) 0%, var(--primary-red) 100%);
  box-shadow: 0 0 10px var(--primary-red);
}

/* ========== GARAGE NAVBAR ========== */
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background:
    linear-gradient(135deg, var(--matte-black) 0%, var(--dark-steel) 50%, var(--matte-black) 100%),
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><rect width="100" height="20" fill="%23000"/><rect x="0" y="0" width="2" height="20" fill="%23dc143c"/><rect x="98" y="0" width="2" height="20" fill="%23dc143c"/></svg>');
  border-bottom: 3px solid var(--primary-red);
  padding: 1.2rem 2rem;
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow:
    0 5px 25px rgba(0,0,0,0.8),
    inset 0 1px 0 rgba(255,255,255,0.1),
    0 0 20px rgba(220, 20, 60, 0.3);
  backdrop-filter: blur(10px);
}

.navbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg,
    transparent 0%,
    var(--primary-red) 20%,
    var(--accent-orange) 50%,
    var(--primary-red) 80%,
    transparent 100%);
  animation: pulse-glow 2s ease-in-out infinite alternate;
}

.nav-logo {
  display: flex;
  align-items: center;
  font-family: 'Rajdhani', sans-serif;
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--accent-orange);
  letter-spacing: 1px;
  text-transform: uppercase;
  position: relative;
}

.nav-logo::before {
  content: '🔧';
  position: absolute;
  left: -25px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.2rem;
}

.logo-img {
  height: 50px;
  border-radius: 10px;
  margin-right: 15px;
  border: 2px solid var(--primary-red);
  box-shadow: 0 0 15px rgba(220, 20, 60, 0.5);
  transition: all 0.3s ease;
}

.logo-img:hover {
  transform: scale(1.1);
  box-shadow: 0 0 25px rgba(220, 20, 60, 0.8);
}

.nav-links {
  display: flex;
  gap: 2.5rem;
  align-items: center;
}

.nav-links a {
  color: var(--chrome-silver);
  text-decoration: none;
  font-family: 'Rajdhani', sans-serif;
  font-weight: 600;
  font-size: 1rem;
  position: relative;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  padding: 6px 12px;
  border: 1px solid transparent;
  border-radius: 20px;
}

.nav-links a::before {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -5px;
  left: 50%;
  background: linear-gradient(90deg, var(--primary-red), var(--accent-orange));
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.nav-links a::after {
  content: '⚡';
  position: absolute;
  left: -25px;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  transition: all 0.3s ease;
  color: var(--accent-orange);
  font-size: 1rem;
}

.nav-links a:hover {
  color: var(--accent-orange);
  transform: translateY(-2px);
  border-color: var(--primary-red);
  background: rgba(220, 20, 60, 0.1);
}

.nav-links a:hover::before {
  width: 100%;
}

.nav-links a:hover::after {
  opacity: 1;
  left: -30px;
}

/* ========== GARAGE ANIMATIONS (STATIC) ========== */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes garage-entrance {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes pulse-glow {
  0% { opacity: 0.6; }
  100% { opacity: 1; }
}

@keyframes border-glow {
  0% { opacity: 0.6; }
  100% { opacity: 1; }
}

/* ========== GARAGE NOTIFICATION BAR ========== */
.notification-bar {
  background:
    linear-gradient(90deg, var(--primary-red) 0%, var(--accent-orange) 50%, var(--primary-red) 100%),
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 50 10"><rect width="50" height="10" fill="%23000"/><rect x="0" y="4" width="50" height="2" fill="%23333"/></svg>');
  color: white;
  padding: 12px 20px;
  text-align: center;
  position: relative;
  display: none;
  z-index: 1001;
  border-bottom: 2px solid var(--accent-orange);
  box-shadow: 0 3px 15px rgba(220, 20, 60, 0.4);
}

.notification-bar::before {
  content: '🔧';
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
}

.notification-bar::after {
  content: '⚙️';
  position: absolute;
  right: 60px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 14px;
}

.notification-bar p {
  margin: 0;
  font-family: 'Rajdhani', sans-serif;
  font-weight: 500;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.notification-bar a {
  color: var(--neon-yellow);
  text-decoration: none;
  font-weight: bold;
  transition: all 0.3s ease;
}

.notification-bar a:hover {
  color: white;
}

#close-notification {
  position: absolute;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  background: rgba(0,0,0,0.3);
  border: 2px solid var(--accent-orange);
  color: white;
  font-size: 16px;
  cursor: pointer;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-weight: bold;
}

#close-notification:hover {
  background: var(--accent-orange);
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 0 10px var(--accent-orange);
}

/* ========== GARAGE QR CODE SECTION ========== */
#qr-code-section {
  text-align: center;
  padding: 4rem 2rem;
  background:
    linear-gradient(135deg, var(--dark-steel) 0%, var(--steel-gray) 50%, var(--dark-steel) 100%),
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><rect width="100" height="100" fill="%23000"/><circle cx="20" cy="20" r="2" fill="%23333" opacity="0.3"/><circle cx="80" cy="80" r="2" fill="%23333" opacity="0.3"/></svg>');
  border-top: 4px solid var(--primary-red);
  position: relative;
  box-shadow: inset 0 5px 15px rgba(0,0,0,0.3);
}

#qr-code-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><text x="50" y="100" font-size="30" opacity="0.02" fill="%23dc143c">🔧⚙️🛠️</text></svg>') repeat;
  background-size: 200px 200px;
  pointer-events: none;
}

#qr-code-section h2 {
  font-family: 'Rajdhani', sans-serif;
  font-size: 2rem;
  color: var(--accent-orange);
  margin-bottom: 1.5rem;
  text-transform: uppercase;
  letter-spacing: 2px;
  font-weight: 700;
  position: relative;
  z-index: 2;
}

#qr-code-section h2::before {
  content: '⚙️';
  position: absolute;
  left: -35px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.5rem;
}

#qr-code-section h2::after {
  content: '⚙️';
  position: absolute;
  right: -35px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.5rem;
}

.qr-code-container {
  margin: 2rem auto;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 280px;
  height: 280px;
  border: 4px solid var(--primary-red);
  border-radius: 20px;
  background:
    linear-gradient(135deg, var(--matte-black) 0%, var(--dark-steel) 100%);
  box-shadow:
    0 0 40px rgba(220, 20, 60, 0.4),
    inset 0 0 30px rgba(0,0,0,0.5);
  position: relative;
  z-index: 2;
}

.qr-code-container::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  background: linear-gradient(45deg, var(--primary-red), var(--accent-orange), var(--primary-red));
  border-radius: 20px;
  z-index: -1;
  animation: border-glow 3s ease-in-out infinite alternate;
}

#generate-qr-btn {
  background: linear-gradient(135deg, var(--primary-red) 0%, var(--accent-orange) 100%);
  color: white;
  padding: 1.2rem 2.5rem;
  border: none;
  font-family: 'Rajdhani', sans-serif;
  font-size: 1.3rem;
  font-weight: 700;
  border-radius: 30px;
  cursor: pointer;
  text-transform: uppercase;
  letter-spacing: 2px;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(220, 20, 60, 0.4);
  position: relative;
  overflow: hidden;
  z-index: 2;
}

#generate-qr-btn::before {
  content: '⚡';
  position: absolute;
  left: -40px;
  top: 50%;
  transform: translateY(-50%);
  transition: all 0.4s ease;
  font-size: 1.5rem;
}

#generate-qr-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.6s ease;
}

#generate-qr-btn:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 35px rgba(220, 20, 60, 0.6);
  text-shadow: 0 0 10px white;
}

#generate-qr-btn:hover::before {
  left: 15px;
}

#generate-qr-btn:hover::after {
  left: 100%;
}

/* ========== GARAGE HERO SECTION ========== */
.hero {
  background:
    linear-gradient(135deg, rgba(220, 20, 60, 0.9) 0%, rgba(255, 69, 0, 0.9) 100%),
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 400"><defs><pattern id="garage-floor" patternUnits="userSpaceOnUse" width="40" height="40"><rect width="40" height="40" fill="%23000"/><rect x="0" y="0" width="40" height="2" fill="%23333"/><rect x="0" y="38" width="40" height="2" fill="%23333"/></pattern></defs><rect width="400" height="400" fill="url(%23garage-floor)"/></svg>');
  height: 70vh;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  margin: 0;
  padding: 0;
  overflow: hidden;
  border-bottom: 6px solid var(--primary-red);
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 30% 30%, rgba(255, 69, 0, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 70% 70%, rgba(220, 20, 60, 0.15) 0%, transparent 50%);
  animation: pulse-bg 6s ease-in-out infinite alternate;
}

.hero::after {
  content: '🏁';
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: 2rem;
  opacity: 0.1;
}

.hero-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    to bottom right,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(0, 0, 0, 0.7) 100%
  );
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.hero-content {
  max-width: 900px;
  padding: 0;
  margin: 0;
  animation: garage-entrance 2.5s ease-out;
  color: white;
  position: relative;
  z-index: 3;
}

.hero-content h1 {
  font-family: 'Rajdhani', sans-serif;
  font-size: 3rem;
  margin: 0 0 1.5rem 0;
  color: var(--accent-orange);
  text-transform: uppercase;
  letter-spacing: 2px;
  line-height: 1.1;
  font-weight: 700;
}

.hero-content h1::before {
  content: '🔧';
  position: absolute;
  left: -50px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 2rem;
}

.hero-content h1::after {
  content: '⚙️';
  position: absolute;
  right: -50px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 2rem;
}

.tagline {
  font-family: 'Rajdhani', sans-serif;
  font-size: 1.2rem;
  margin-bottom: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: var(--chrome-silver);
}

.rating {
  font-size: 1rem;
  margin-bottom: 2rem;
  color: var(--neon-yellow);
  font-weight: 500;
}

.hero-btn {
  background: linear-gradient(135deg, var(--primary-red) 0%, var(--accent-orange) 100%);
  padding: 0.8rem 1.8rem;
  color: #fff;
  text-decoration: none;
  font-family: 'Rajdhani', sans-serif;
  font-size: 1rem;
  font-weight: 700;
  border-radius: 25px;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.4s ease;
  margin: 0 10px;
  display: inline-block;
  position: relative;
  overflow: hidden;
  border: 2px solid transparent;
  box-shadow: 0 5px 20px rgba(220, 20, 60, 0.4);
}

.hero-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.6s ease;
}

.hero-btn::after {
  content: '⚡';
  position: absolute;
  right: -30px;
  top: 50%;
  transform: translateY(-50%);
  transition: all 0.4s ease;
  font-size: 1.2rem;
}

.hero-btn:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(220, 20, 60, 0.6);
  border-color: var(--neon-yellow);
}

.hero-btn:hover::before {
  left: 100%;
}

.hero-btn:hover::after {
  right: 20px;
}

.hero-btn.swiggy {
  background: linear-gradient(135deg, #FC8019 0%, #ff6b35 100%);
  box-shadow: 0 8px 30px rgba(252, 128, 25, 0.4);
}

.hero-btn.swiggy:hover {
  box-shadow: 0 15px 40px rgba(252, 128, 25, 0.6);
}

.hero-btn.zomato {
  background: linear-gradient(135deg, #E23744 0%, #ff4757 100%);
  box-shadow: 0 8px 30px rgba(226, 55, 68, 0.4);
}

.hero-btn.zomato:hover {
  box-shadow: 0 15px 40px rgba(226, 55, 68, 0.6);
}

/* ========== GARAGE GALLERY SECTION ========== */
.gallery {
  padding: 5rem 2rem;
  background:
    linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(45, 45, 45, 0.95) 100%),
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><rect width="200" height="200" fill="%23000"/><rect x="0" y="0" width="200" height="4" fill="%23333"/><rect x="0" y="196" width="200" height="4" fill="%23333"/><rect x="0" y="0" width="4" height="200" fill="%23333"/><rect x="196" y="0" width="4" height="200" fill="%23333"/></svg>');
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 25px;
  box-shadow:
    0 15px 50px rgba(0, 0, 0, 0.6),
    inset 0 2px 0 rgba(255, 255, 255, 0.1);
  text-align: center;
  max-width: 1300px;
  margin: 5rem auto;
  border: 3px solid var(--primary-red);
  position: relative;
}

.gallery::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(45deg, var(--primary-red), var(--accent-orange), var(--primary-red));
  border-radius: 25px;
  z-index: -1;
  animation: border-glow 4s ease-in-out infinite alternate;
}

.gallery h2 {
  font-family: 'Rajdhani', sans-serif;
  font-size: 2.5rem;
  color: var(--accent-orange);
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 2px;
  font-weight: 700;
  position: relative;
}

.gallery h2::before {
  content: '📸';
  position: absolute;
  left: -50px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.8rem;
}

.gallery h2::after {
  content: '🎯';
  position: absolute;
  right: -50px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.8rem;
}

.gallery-subtitle {
  font-family: 'Rajdhani', sans-serif;
  font-size: 1.1rem;
  color: var(--chrome-silver);
  margin-bottom: 2.5rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 400;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.gallery-item {
  overflow: hidden;
  border-radius: 20px;
  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.6),
    inset 0 2px 0 rgba(255, 255, 255, 0.1);
  background: linear-gradient(135deg, var(--dark-steel) 0%, var(--steel-gray) 100%);
  transition: all 0.5s ease;
  height: 250px;
  border: 2px solid var(--steel-gray);
  position: relative;
}

.gallery-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(220, 20, 60, 0.15) 50%, transparent 70%);
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: 2;
}

.gallery-item::after {
  content: '🔍';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 3rem;
  opacity: 0;
  transition: all 0.4s ease;
  z-index: 3;
  color: var(--accent-orange);
  text-shadow: 0 0 20px var(--accent-orange);
}

.gallery-item:hover {
  transform: translateY(-10px) scale(1.03);
  box-shadow:
    0 20px 50px rgba(220, 20, 60, 0.4),
    0 0 40px rgba(255, 69, 0, 0.3);
  border-color: var(--primary-red);
}

.gallery-item:hover::before {
  opacity: 1;
}

.gallery-item:hover::after {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1.2);
}

.gallery-item img {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: cover;
  transition: all 0.5s ease;
  filter: brightness(0.8) contrast(1.1) saturate(0.9);
}

.gallery-item:hover img {
  filter: brightness(1.2) contrast(1.3) saturate(1.3);
  transform: scale(1.1);
}

/* ========== GARAGE LIGHTBOX ========== */
#lightbox-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.95);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  display: none;
  backdrop-filter: blur(15px);
}

#lightbox-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text x="50" y="50" font-size="20" opacity="0.02" fill="%23dc143c">🔧⚙️</text></svg>') repeat;
  background-size: 100px 100px;
  pointer-events: none;
}

#lightbox-overlay img {
  max-width: 90%;
  max-height: 90%;
  border-radius: 20px;
  box-shadow:
    0 0 60px rgba(220, 20, 60, 0.6),
    0 0 120px rgba(255, 69, 0, 0.4);
  border: 4px solid var(--primary-red);
  animation: garage-entrance 0.6s ease;
  position: relative;
  z-index: 2;
}

/* ========== GARAGE MENU SECTION ========== */
#menu {
  background:
    linear-gradient(135deg, var(--dark-steel) 0%, var(--steel-gray) 50%, var(--dark-steel) 100%),
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><rect width="100" height="100" fill="%23000"/><rect x="10" y="10" width="80" height="2" fill="%23333"/><rect x="10" y="88" width="80" height="2" fill="%23333"/></svg>');
  padding: 5rem 2rem;
  border-top: 6px solid var(--primary-red);
  border-radius: 25px;
  box-shadow:
    0 15px 50px rgba(0, 0, 0, 0.6),
    inset 0 2px 0 rgba(255, 255, 255, 0.1);
  max-width: 1200px;
  margin: 4rem auto;
  border: 3px solid var(--steel-gray);
  position: relative;
}

#menu::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(45deg, var(--primary-red), var(--accent-orange), var(--primary-red));
  border-radius: 25px;
  z-index: -1;
  animation: border-glow 4s ease-in-out infinite alternate;
}

#menu h2 {
  text-align: center;
  font-family: 'Rajdhani', sans-serif;
  font-size: 2.8rem;
  margin-bottom: 2.5rem;
  color: var(--accent-orange);
  text-transform: uppercase;
  font-weight: 700;
  letter-spacing: 2px;
  position: relative;
}

#menu h2::before {
  content: '🍽️';
  position: absolute;
  left: -60px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 2rem;
}

#menu h2::after {
  content: '🔥';
  position: absolute;
  right: -60px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 2rem;
}

.menu-category {
  margin-bottom: 2rem;
}

.menu-toggle {
  background:
    linear-gradient(135deg, var(--steel-gray) 0%, var(--dark-steel) 100%),
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 50 20"><rect width="50" height="20" fill="%23000"/><rect x="0" y="9" width="50" height="2" fill="%23333"/></svg>');
  border: none;
  border-left: 4px solid var(--primary-red);
  border-radius: 12px;
  box-shadow:
    0 3px 15px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  color: var(--chrome-silver);
  cursor: pointer;
  font-family: 'Rajdhani', sans-serif;
  font-size: 1.1rem;
  font-weight: 600;
  padding: 12px 20px;
  text-align: left;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.4s ease;
  width: 100%;
  position: relative;
  overflow: hidden;
}

.menu-toggle::before {
  content: '⚙️';
  position: absolute;
  right: 25px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.2rem;
  transition: all 0.4s ease;
}

.menu-toggle::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(220, 20, 60, 0.2), transparent);
  transition: left 0.6s ease;
}

.menu-toggle:hover {
  transform: translateX(10px);
  background: linear-gradient(135deg, var(--primary-red) 0%, var(--accent-orange) 100%);
  color: white;
  border-left-color: var(--neon-yellow);
  box-shadow:
    0 8px 30px rgba(220, 20, 60, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.menu-toggle:hover::before {
  transform: translateY(-50%) rotate(180deg);
  color: var(--neon-yellow);
}

.menu-toggle:hover::after {
  left: 100%;
}

.menu-items {
  list-style: none;
  padding: 0;
  margin-top: 1rem;
  display: none;
  background: linear-gradient(135deg, var(--matte-black) 0%, var(--dark-steel) 100%);
  border-radius: 15px;
  border: 2px solid var(--steel-gray);
  box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.3);
}

.menu-items li {
  padding: 12px 20px;
  font-size: 0.95rem;
  font-family: 'Rajdhani', sans-serif;
  font-weight: 500;
  border-bottom: 1px solid var(--steel-gray);
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
  color: var(--chrome-silver);
  position: relative;
}

.menu-items li:last-child {
  border-bottom: none;
}

.menu-items li::before {
  content: '🔧';
  position: absolute;
  left: -30px;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  transition: all 0.3s ease;
  color: var(--accent-orange);
}

.menu-items li:hover {
  background: linear-gradient(90deg, var(--primary-red), var(--accent-orange));
  color: white;
  cursor: pointer;
  transform: translateX(10px);
  text-shadow: 0 0 5px white;
}

.menu-items li:hover::before {
  opacity: 1;
  left: 5px;
}

.menu-items li .price {
  color: var(--neon-yellow);
  font-weight: 700;
}
/* ========== GARAGE INFO SECTIONS ========== */
#location, #contact, #reviews {
  margin-top: 5rem;
  padding: 4rem 2rem;
  border-top: 4px solid var(--primary-red);
  background:
    linear-gradient(135deg, var(--dark-steel) 0%, var(--steel-gray) 50%, var(--dark-steel) 100%),
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><rect width="100" height="100" fill="%23000"/><rect x="20" y="20" width="60" height="2" fill="%23333"/><rect x="20" y="78" width="60" height="2" fill="%23333"/></svg>');
  border-radius: 25px;
  box-shadow:
    0 10px 40px rgba(0, 0, 0, 0.5),
    inset 0 2px 0 rgba(255, 255, 255, 0.1);
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
}

#location::before, #contact::before, #reviews::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, var(--primary-red), var(--accent-orange), var(--primary-red));
  border-radius: 25px;
  z-index: -1;
  animation: border-glow 4s ease-in-out infinite alternate;
}

#location h2, #contact h2, #reviews h2 {
  font-family: 'Rajdhani', sans-serif;
  font-size: 2.2rem;
  color: var(--accent-orange);
  margin-bottom: 2rem;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 2px;
  font-weight: 700;
  position: relative;
}

#location h2::before {
  content: '📍';
  position: absolute;
  left: -40px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.8rem;
}

#contact h2::before {
  content: '📞';
  position: absolute;
  left: -40px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.8rem;
}

#reviews h2::before {
  content: '⭐';
  position: absolute;
  left: -40px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.8rem;
}

.info-box {
  background:
    linear-gradient(135deg, var(--matte-black) 0%, var(--dark-steel) 100%),
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 50 50"><rect width="50" height="50" fill="%23000"/><rect x="5" y="23" width="40" height="4" fill="%23333"/></svg>');
  padding: 2.5rem;
  border-left: 6px solid var(--primary-red);
  border-radius: 20px;
  max-width: 800px;
  margin: 0 auto 3rem;
  box-shadow:
    0 8px 30px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  text-align: left;
  border: 2px solid var(--steel-gray);
  position: relative;
}

.info-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(220, 20, 60, 0.05) 50%, transparent 70%);
  border-radius: 20px;
  pointer-events: none;
}

.info-box p {
  margin: 0.8rem 0;
  font-size: 1rem;
  color: var(--chrome-silver);
  font-family: 'Rajdhani', sans-serif;
  font-weight: 400;
  line-height: 1.6;
}

.info-box a {
  color: var(--accent-orange);
  text-decoration: none;
  font-weight: bold;
  transition: all 0.3s ease;
}

.info-box a:hover {
  color: var(--neon-yellow);
  text-decoration: underline;
}

.map-container {
  max-width: 800px;
  margin: 0 auto;
  border-radius: 20px;
  overflow: hidden;
  box-shadow:
    0 10px 40px rgba(0, 0, 0, 0.5),
    0 0 30px rgba(220, 20, 60, 0.3);
  border: 3px solid var(--primary-red);
  position: relative;
}

.map-container::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(45deg, var(--primary-red), var(--accent-orange), var(--primary-red));
  border-radius: 20px;
  z-index: -1;
  animation: border-glow 3s ease-in-out infinite alternate;
}

.map-container iframe {
  width: 100%;
  height: 350px;
  border: none;
  filter: brightness(0.8) contrast(1.2);
}

/* ========== GARAGE RESERVATION SECTION ========== */

.reservation-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 3rem;
  max-width: 1100px;
  margin: 0 auto;
}

.reservation-form {
  flex: 1 1 400px;
  background:
    linear-gradient(135deg, var(--matte-black) 0%, var(--dark-steel) 100%),
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 50 50"><rect width="50" height="50" fill="%23000"/><rect x="5" y="23" width="40" height="4" fill="%23333"/></svg>');
  padding: 2.5rem;
  border-radius: 20px;
  box-shadow:
    0 10px 40px rgba(0, 0, 0, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  border: 2px solid var(--steel-gray);
  position: relative;
}

.reservation-form::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, var(--primary-red), var(--accent-orange), var(--primary-red));
  border-radius: 20px;
  z-index: -1;
  animation: border-glow 4s ease-in-out infinite alternate;
}

.reservation-form label {
  font-family: 'Rajdhani', sans-serif;
  font-weight: 500;
  font-size: 1rem;
  color: var(--chrome-silver);
  margin-bottom: 0.4rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.reservation-form input,
.reservation-form select {
  padding: 0.8rem;
  font-size: 0.95rem;
  border-radius: 8px;
  border: 2px solid var(--steel-gray);
  background: var(--dark-steel);
  color: var(--chrome-silver);
  font-family: 'Rajdhani', sans-serif;
  transition: all 0.3s ease;
}

.reservation-form input:focus,
.reservation-form select:focus {
  outline: none;
  border-color: var(--primary-red);
  box-shadow: 0 0 15px rgba(220, 20, 60, 0.3);
  background: var(--matte-black);
}

.reservation-form button {
  background: linear-gradient(135deg, var(--primary-red) 0%, var(--accent-orange) 100%);
  color: white;
  padding: 0.9rem;
  border: none;
  font-family: 'Rajdhani', sans-serif;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 12px;
  cursor: pointer;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(220, 20, 60, 0.3);
  position: relative;
  overflow: hidden;
}

.reservation-form button::before {
  content: '🔧';
  position: absolute;
  left: -30px;
  top: 50%;
  transform: translateY(-50%);
  transition: all 0.4s ease;
}

.reservation-form button:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(220, 20, 60, 0.5);
  text-shadow: 0 0 10px white;
}

.reservation-form button:hover::before {
  left: 20px;
}

.reservation-info {
  flex: 1 1 350px;
  background:
    linear-gradient(135deg, var(--steel-gray) 0%, var(--dark-steel) 100%),
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 50 50"><rect width="50" height="50" fill="%23000"/><circle cx="25" cy="25" r="2" fill="%23333"/></svg>');
  padding: 2.5rem;
  border-left: 6px solid var(--accent-orange);
  border-radius: 20px;
  border: 2px solid var(--steel-gray);
  box-shadow:
    0 8px 30px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.reservation-info h3 {
  margin-top: 0;
  color: var(--accent-orange);
  font-family: 'Rajdhani', sans-serif;
  font-size: 1.4rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 700;
}

.reservation-info p {
  margin: 0.8rem 0;
  color: var(--chrome-silver);
  font-family: 'Rajdhani', sans-serif;
  font-size: 0.95rem;
  line-height: 1.5;
}

/* ========== GARAGE REVIEW SECTION ========== */

.review-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 2.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.review-box {
  background:
    linear-gradient(135deg, var(--matte-black) 0%, var(--dark-steel) 100%),
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 50 50"><rect width="50" height="50" fill="%23000"/><rect x="5" y="5" width="40" height="2" fill="%23333"/><rect x="5" y="43" width="40" height="2" fill="%23333"/></svg>');
  border-left: 6px solid var(--accent-orange);
  box-shadow:
    0 8px 30px rgba(0, 0, 0, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  padding: 2rem;
  max-width: 350px;
  text-align: left;
  border-radius: 20px;
  border: 2px solid var(--steel-gray);
  position: relative;
  transition: all 0.3s ease;
}

.review-box::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, var(--accent-orange), var(--primary-red), var(--accent-orange));
  border-radius: 20px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.review-box:hover {
  transform: translateY(-5px);
  box-shadow:
    0 15px 40px rgba(0, 0, 0, 0.6),
    0 0 30px rgba(255, 69, 0, 0.3);
}

.review-box:hover::before {
  opacity: 1;
}

.review-box blockquote {
  font-style: italic;
  margin: 0 0 0.8rem;
  color: var(--chrome-silver);
  font-family: 'Rajdhani', sans-serif;
  font-size: 1rem;
  line-height: 1.5;
  position: relative;
}

.review-box blockquote::before {
  content: '"';
  position: absolute;
  left: -15px;
  top: -8px;
  font-size: 2rem;
  color: var(--accent-orange);
  opacity: 0.3;
}

.review-box p {
  margin: 0;
  font-weight: 600;
  color: var(--accent-orange);
  font-family: 'Rajdhani', sans-serif;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* ========== GARAGE FOOTER ========== */
footer {
  background:
    linear-gradient(135deg, var(--matte-black) 0%, var(--dark-steel) 100%),
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><rect width="100" height="20" fill="%23000"/><rect x="0" y="9" width="100" height="2" fill="%23333"/></svg>');
  color: var(--chrome-silver);
  text-align: center;
  padding: 2rem 1.5rem;
  font-size: 0.9rem;
  margin-top: 4rem;
  border-top: 3px solid var(--primary-red);
  position: relative;
}

footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg,
    transparent 0%,
    var(--primary-red) 20%,
    var(--accent-orange) 50%,
    var(--primary-red) 80%,
    transparent 100%);
  animation: pulse-glow 3s ease-in-out infinite alternate;
}

footer p {
  font-family: 'Rajdhani', sans-serif;
  font-weight: 500;
  margin: 1rem 0;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* ========== ADDITIONAL GARAGE ANIMATIONS ========== */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spark {
  0%, 100% { opacity: 0; }
  50% { opacity: 1; }
}

@keyframes engine-rev {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* ========== GARAGE INSTAGRAM LINKS ========== */
footer a.instagram-link {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  font-weight: 600;
  color: var(--accent-orange);
  font-size: 0.95rem;
  font-family: 'Rajdhani', sans-serif;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  padding: 8px 16px;
  border: 2px solid var(--accent-orange);
  border-radius: 20px;
  background: rgba(255, 69, 0, 0.1);
}

footer a.instagram-link:hover {
  transform: translateY(-3px) scale(1.05);
  color: var(--neon-yellow);
  border-color: var(--neon-yellow);
  box-shadow: 0 5px 20px rgba(255, 255, 0, 0.3);
}

footer a.instagram-link img {
  width: 28px;
  height: 28px;
  border-radius: 8px;
  border: 1px solid var(--accent-orange);
  transition: all 0.3s ease;
}

footer a.instagram-link:hover img {
  border-color: var(--neon-yellow);
}

/* ========== GARAGE INSTAGRAM FEED SECTION ========== */
.instagram-feed {
  padding: 5rem 2rem;
  text-align: center;
  background:
    linear-gradient(135deg, var(--dark-steel) 0%, var(--steel-gray) 50%, var(--dark-steel) 100%),
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><rect width="100" height="100" fill="%23000"/><rect x="20" y="20" width="60" height="2" fill="%23333"/><rect x="20" y="78" width="60" height="2" fill="%23333"/></svg>');
  border-top: 4px solid var(--primary-red);
  border-bottom: 4px solid var(--primary-red);
  position: relative;
}

.instagram-feed::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><text x="100" y="100" font-size="30" opacity="0.02" fill="%23ff4500">📸🔧⚙️</text></svg>') repeat;
  background-size: 200px 200px;
  pointer-events: none;
}

.instagram-feed h2 {
  font-family: 'Rajdhani', sans-serif;
  font-size: 2.5rem;
  color: var(--accent-orange);
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 2px;
  font-weight: 700;
  position: relative;
  z-index: 2;
}

.instagram-feed h2::before {
  content: '📸';
  position: absolute;
  left: -50px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 2rem;
}

.instagram-feed p {
  font-family: 'Rajdhani', sans-serif;
  font-size: 1.1rem;
  color: var(--chrome-silver);
  margin-bottom: 2.5rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 400;
  position: relative;
  z-index: 2;
}

.instagram-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  max-width: 900px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.instagram-post {
  position: relative;
  overflow: hidden;
  border-radius: 15px;
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  border: 2px solid var(--steel-gray);
  height: 200px;
}

.instagram-post::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, var(--primary-red), var(--accent-orange), var(--primary-red));
  border-radius: 20px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.instagram-post:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 20px 50px rgba(0, 0, 0, 0.7),
    0 0 40px rgba(255, 69, 0, 0.3);
}

.instagram-post:hover::before {
  opacity: 1;
}

.instagram-post img {
  width: 100%;
  display: block;
  transition: all 0.4s ease;
  filter: brightness(0.8) contrast(1.1);
}

.instagram-post:hover img {
  transform: scale(1.1);
  filter: brightness(1.1) contrast(1.2) saturate(1.2);
}

.instagram-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(220, 20, 60, 0.8), rgba(255, 69, 0, 0.8));
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.instagram-post:hover .instagram-overlay {
  opacity: 1;
}

.instagram-likes, .instagram-comments {
  font-family: 'Rajdhani', sans-serif;
  font-size: 1.1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.instagram-follow-btn {
  display: inline-block;
  margin-top: 2.5rem;
  padding: 0.9rem 2rem;
  background: linear-gradient(135deg, var(--primary-red) 0%, var(--accent-orange) 100%);
  color: #fff;
  text-decoration: none;
  font-family: 'Rajdhani', sans-serif;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 25px;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(220, 20, 60, 0.3);
  position: relative;
  z-index: 2;
  overflow: hidden;
}

.instagram-follow-btn::before {
  content: '📸';
  position: absolute;
  left: -30px;
  top: 50%;
  transform: translateY(-50%);
  transition: all 0.4s ease;
}

.instagram-follow-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(220, 20, 60, 0.5);
}

.instagram-follow-btn:hover::before {
  left: 20px;
}

/* ========== GARAGE INSTAGRAM REELS GRID ========== */
.embed-reels {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  max-width: 900px;
  margin: 2rem auto 0;
  justify-items: center;
  position: relative;
  z-index: 2;
}

.instagram-media {
  background: linear-gradient(135deg, var(--dark-steel) 0%, var(--steel-gray) 100%) !important;
  border: 2px solid var(--primary-red) !important;
  border-radius: 15px !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5) !important, 0 0 20px rgba(220, 20, 60, 0.2) !important;
  margin: 0 !important;
  max-width: 300px !important;
  width: 100% !important;
  min-width: 280px !important;
  transition: all 0.3s ease !important;
  position: relative !important;
}

.instagram-media::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(45deg, var(--primary-red), var(--accent-orange), var(--primary-red));
  border-radius: 20px;
  z-index: -1;
  animation: border-glow 4s ease-in-out infinite alternate;
}

.instagram-media:hover {
  transform: translateY(-5px) scale(1.01) !important;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.7) !important, 0 0 30px rgba(255, 69, 0, 0.3) !important;
}

/* ========== GARAGE FLOATING CTA BUTTONS ========== */
.floating-cta {
  position: fixed;
  bottom: 30px;
  right: 30px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  z-index: 1001;
}

.floating-cta .cta-btn {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  border-radius: 25px;
  text-decoration: none;
  color: white;
  font-family: 'Rajdhani', sans-serif;
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow:
    0 5px 20px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
}

.floating-cta .cta-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.6s ease;
}

.floating-cta .cta-btn:hover {
  transform: translateY(-5px) scale(1.1);
  border-color: var(--neon-yellow);
}

.floating-cta .cta-btn:hover::before {
  left: 100%;
}

.floating-cta .cta-btn img {
  width: 28px;
  height: 28px;
  margin-right: 10px;
  border-radius: 5px;
}

.floating-cta .swiggy {
  background: linear-gradient(135deg, #FC8019 0%, #ff6b35 100%);
  box-shadow:
    0 8px 25px rgba(252, 128, 25, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.floating-cta .swiggy:hover {
  box-shadow:
    0 15px 40px rgba(252, 128, 25, 0.6),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.floating-cta .zomato {
  background: linear-gradient(135deg, #E23744 0%, #ff4757 100%);
  box-shadow:
    0 8px 25px rgba(226, 55, 68, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.floating-cta .zomato:hover {
  box-shadow:
    0 15px 40px rgba(226, 55, 68, 0.6),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* ========== GARAGE RESPONSIVE DESIGN ========== */

/* Large Tablets and smaller desktops */
@media (max-width: 1024px) {
  .hero-content h1 {
    font-size: 4rem;
  }

  .gallery-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
  }

  .instagram-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
  }

  #menu h2, .gallery h2, .instagram-feed h2 {
    font-size: 3.5rem;
  }
}

/* Tablets */
@media (max-width: 768px) {
  .navbar {
    flex-direction: column;
    padding: 1.5rem 1rem;
    gap: 1rem;
  }

  .nav-logo {
    font-size: 1.6rem;
  }

  .nav-logo::before {
    left: -25px;
    font-size: 1.2rem;
  }

  .nav-links {
    margin-top: 1rem;
    gap: 1.5rem;
    flex-wrap: wrap;
    justify-content: center;
  }

  .nav-links a {
    font-size: 1rem;
    padding: 6px 12px;
  }

  .hero {
    height: 60vh;
  }

  .hero-content h1 {
    font-size: 3rem;
    letter-spacing: 2px;
  }

  .hero-content h1::before,
  .hero-content h1::after {
    display: none;
  }

  .tagline {
    font-size: 1.3rem;
    letter-spacing: 2px;
  }

  .hero-btn {
    font-size: 1.1rem;
    padding: 1rem 2rem;
    margin: 0 8px;
  }

  .gallery-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .gallery h2, .instagram-feed h2 {
    font-size: 3rem;
  }

  .gallery h2::before,
  .gallery h2::after,
  .instagram-feed h2::before {
    display: none;
  }

  #menu h2 {
    font-size: 3rem;
  }

  #menu h2::before,
  #menu h2::after {
    display: none;
  }

  .reservation-container {
    flex-direction: column;
    gap: 2rem;
  }

  .review-container {
    flex-direction: column;
    align-items: center;
    gap: 2rem;
  }

  .embed-reels {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .floating-cta {
    bottom: 15px;
    right: 15px;
    gap: 10px;
  }

  .floating-cta .cta-btn {
    padding: 12px 16px;
    font-size: 1rem;
  }

  #location h2::before,
  #contact h2::before,
  #reviews h2::before {
    display: none;
  }
}

/* Small Mobile devices */
@media (max-width: 480px) {
  .nav-logo {
    font-size: 1.4rem;
    letter-spacing: 1px;
  }

  .nav-logo::before {
    display: none;
  }

  .nav-links a {
    font-size: 0.9rem;
    padding: 4px 8px;
  }

  .hero {
    height: 50vh;
  }

  .hero-content h1 {
    font-size: 2.2rem;
    letter-spacing: 1px;
  }

  .tagline {
    font-size: 1.1rem;
    letter-spacing: 1px;
  }

  .hero-btn {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
    margin: 0 5px;
  }

  #menu h2, .gallery h2, .instagram-feed h2 {
    font-size: 2.5rem;
    letter-spacing: 2px;
  }

  .menu-toggle {
    font-size: 1.1rem;
    padding: 15px 20px;
  }

  .menu-items li {
    padding: 12px 20px;
    font-size: 1rem;
  }

  .floating-cta {
    bottom: 10px;
    right: 10px;
  }

  .floating-cta .cta-btn {
    padding: 10px 12px;
    font-size: 0.9rem;
  }

  .floating-cta .cta-btn span {
    display: none;
  }

  .floating-cta .cta-btn img {
    margin-right: 0;
    width: 24px;
    height: 24px;
  }

  .info-box {
    padding: 2rem;
    margin: 0 auto 2rem;
  }

  .review-box {
    max-width: 100%;
    margin: 0 auto 2rem;
  }

  .reservation-form,
  .reservation-info {
    padding: 2rem;
  }

  #qr-code-section h2 {
    font-size: 2rem;
    letter-spacing: 2px;
  }

  #qr-code-section h2::before,
  #qr-code-section h2::after {
    display: none;
  }
}

/* ========== END GARAGE THEME ========== */
