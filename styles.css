@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');

/* General St<PERSON>ing */
body, html {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Segoe UI', sans-serif;
  background: linear-gradient(135deg, #fff7f0, #ffe6cc, #ffcc99);
  background-attachment: fixed;
  background-size: cover;
  color: #333;
  line-height: 1.6;
}

/* Navbar Styling */
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(to right, #1a1a1a, #292929);
  padding: 1rem 2rem;
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.nav-logo {
  display: flex;
  align-items: center;
  font-size: 1.5rem;
  font-weight: bold;
  color: #f39c12;
  letter-spacing: 1px;
}

.logo-img {
  height: 40px;
  border-radius: 25%;
  margin-right: 10px;
}

.nav-links {
  display: flex;
  gap: 1.5rem;
}

.nav-links a {
  color: #ffffff;
  text-decoration: none;
  font-weight: 500;
  position: relative;
  transition: color 0.3s ease;
  font-size: 1rem;
}

.nav-links a::after {
  content: '';
  position: absolute;
  width: 0%;
  height: 2px;
  bottom: -4px;
  left: 0;
  background-color: #f39c12;
  transition: width 0.3s;
}

.nav-links a:hover {
  color: #f39c12;
}

.nav-links a:hover::after {
  width: 100%;
}

/* Dynamic Notification Bar */
.notification-bar {
  background: linear-gradient(90deg, #FC8019, #E23744);
  color: white;
  padding: 10px 20px;
  text-align: center;
  position: relative;
  display: none; /* Hidden by default */
  z-index: 1001;
}

.notification-bar p {
  margin: 0;
  font-weight: 500;
}

.notification-bar a {
  color: white;
  text-decoration: underline;
  font-weight: bold;
}

#close-notification {
  position: absolute;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
}

/* QR Code Section */
#qr-code-section {
  text-align: center;
  padding: 3rem 2rem;
  background-color: #fffefb;
  border-top: 1px solid #ddd;
}

#qr-code-section h2 {
  font-size: 2rem;
  color: #333;
  margin-bottom: 1rem;
}

.qr-code-container {
  margin: 1.5rem auto;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 256px;
  height: 256px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

#generate-qr-btn {
  background-color: #d35400;
  color: white;
  padding: 0.75rem 1.5rem;
  border: none;
  font-size: 1rem;
  border-radius: 5px;
  cursor: pointer;
  transition: background 0.3s ease;
}

#generate-qr-btn:hover {
  background-color: #e67e22;
}

/* Hero Section */
.hero {
  background: linear-gradient(135deg, #f39c12, #d35400);
  height: 50vh;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.hero-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(to bottom right, rgba(0, 0, 0, 0.65), rgba(0, 0, 0, 0.3));
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.hero-content {
  max-width: 700px;
  padding: 0;
  margin: 0;
  animation: fadeIn 1.5s ease-in-out;
  color: white;
}

.hero-content h1 {
  font-size: 3rem;
  margin: 0 0 1rem 0;
  color: #f39c12;
  text-shadow: 2px 2px 10px rgba(0,0,0,0.7);
}

.tagline {
  font-size: 1.2rem;
  margin-bottom: 0.3rem;
}

.rating {
  font-size: 1rem;
  margin-bottom: 1.5rem;
}

.hero-btn {
  background-color: #f39c12;
  padding: 0.8rem 1.5rem;
  color: #fff;
  text-decoration: none;
  font-size: 1rem;
  border-radius: 30px;
  transition: background 0.3s;
}

.hero-btn:hover {
  background-color: #d35400;
}

.hero-btn.swiggy {
  background-color: #FC8019;
}

.hero-btn.zomato {
  background-color: #E23744;
}

/* Gallery Section */
.gallery {
  padding: 3rem 2rem;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 1200px;
  margin: 3rem auto;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.gallery h2 {
  font-size: 2.2rem;
  color: #d35400;
  margin-bottom: 0.5rem;
}

.gallery-subtitle {
  font-size: 1rem;
  color: #666;
  margin-bottom: 2rem;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  max-width: 1100px;
  margin: 0 auto;
}

.gallery-item {
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  background-color: #fff;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 200px;
}

.gallery-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.gallery-item img {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: cover;
  transition: filter 0.3s;
}

.gallery-item:hover img {
  filter: brightness(1.05);
}

/* Lightbox Overlay */
#lightbox-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.85);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  display: none;
}

#lightbox-overlay img {
  max-width: 90%;
  max-height: 90%;
  border-radius: 10px;
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
  animation: fadeIn 0.4s ease;
}

/* Menu Section */
#menu {
  background-color: #fffefb;
  padding: 3rem 2rem;
  border-top: 4px solid #f39c12;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  max-width: 1000px;
  margin: 2rem auto;
}

#menu h2 {
  text-align: center;
  font-size: 3rem;
  margin-bottom: 2rem;
  color: #d35400;
  background: linear-gradient(90deg, #f39c12, #e67e22);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: fadeInDown 1s ease-out;
  text-transform: uppercase;
  font-weight: 800;
  letter-spacing: 2px;
}

.menu-category {
  margin-bottom: 1.5rem;
}

.menu-toggle {
  background: linear-gradient(90deg, #ffd9b3, #fff4e6);
  border: none;
  border-left: 8px solid #f39c12;
  border-radius: 4px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.05);
  color: #2c3e50;
  cursor: pointer;
  font-family: 'Poppins', sans-serif;
  font-size: 1.2rem;
  padding: 12px;
  text-align: left;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease-in-out;
  width: 100%;
}

.menu-toggle:hover {
  transform: scale(1.02);
  background: linear-gradient(90deg, #ffe0b3, #ffcc80);
  color: #d35400;
  border-left-color: #e67e22;
}

.menu-items {
  list-style: none;
  padding-left: 1rem;
  margin-top: 1rem;
  display: none; /* Hidden by default */
}

.menu-items li {
  padding: 8px 0;
  font-size: 0.9rem;
  font-family: 'Poppins', sans-serif;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  transition: background 0.3s ease;
}

.menu-items li:hover {
  background: #fff8f0;
  cursor: pointer;
}
#location, #contact, #reviews {
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid #ddd;
}
#location h2, #contact h2, #reviews h2 {
    font-size: 2rem;
    color: #333;
    margin-bottom: 1.5rem;
}

.info-box {
  background-color: #fff;
  padding: 1.5rem;
  border-left: 5px solid #f39c12;
  border-radius: 8px;
  max-width: 700px;
  margin: 0 auto 2rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  text-align: left;
}

.info-box p {
  margin: 0.5rem 0;
  font-size: 1rem;
  color: #333;
}

.info-box a {
  color: #3498db;
  text-decoration: none;
  font-weight: bold;
}

.info-box a:hover {
  text-decoration: underline;
}

.map-container {
  max-width: 700px;
  margin: 0 auto;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.map-container iframe {
  width: 100%;
  height: 300px;
  border: none;
}

/* Remove redundant styling */

.reservation-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 2rem;
  max-width: 900px;
  margin: 0 auto;
}

.reservation-form {
  flex: 1 1 300px;
  background: #fff;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.reservation-form label {
  font-weight: bold;
  margin-bottom: 0.2rem;
}

.reservation-form input,
.reservation-form select {
  padding: 0.6rem;
  font-size: 1rem;
  border-radius: 5px;
  border: 1px solid #ccc;
}

.reservation-form button {
  background-color: #d35400;
  color: white;
  padding: 0.75rem;
  border: none;
  font-size: 1rem;
  border-radius: 5px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.reservation-form button:hover {
  background-color: #e67e22;
}

.reservation-info {
  flex: 1 1 250px;
  background: #fff8f0;
  padding: 1.5rem;
  border-left: 4px solid #f39c12;
  border-radius: 8px;
}

.reservation-info h3 {
  margin-top: 0;
  color: #2c3e50;
}

.reservation-info p {
  margin: 0.5rem 0;
}

/* Remove redundant styling */

.review-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1.5rem;
}

.review-box {
  background-color: #fff;
  border-left: 5px solid #f39c12;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
  padding: 1rem;
  max-width: 300px;
  text-align: left;
  border-radius: 8px;
}

.review-box blockquote {
  font-style: italic;
  margin: 0 0 0.5rem;
  color: #2c3e50;
}

.review-box p {
  margin: 0;
  font-weight: bold;
  color: #555;
}

/* Footer */
footer {
  background: rgba(0, 0, 0, 0.8);
  color: #f0f0f0;
  text-align: center;
  padding: 1rem;
  font-size: 0.9rem;
  margin-top: 2rem;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
footer a.instagram-link {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  font-weight: bold;
  color: #E1306C; /* Instagram pink */
  font-size: 16px;
  transition: transform 0.2s ease, color 0.2s ease;
}

footer a.instagram-link:hover {
  transform: scale(1.05);
  color: #C13584; /* Slightly darker on hover */
}

footer a.instagram-link img {
  width: 24px;
  height: 24px;
  border-radius: 5px;
}

/* Instagram Feed Section */
.instagram-feed {
  padding: 3rem 2rem;
  text-align: center;
  background: #fff;
}

.instagram-feed h2 {
  font-size: 2.5rem;
  color: #d35400;
  margin-bottom: 0.5rem;
}

.instagram-feed p {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 2rem;
}

.instagram-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.instagram-post {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 8px 16px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.instagram-post:hover {
  transform: translateY(-5px);
}

.instagram-post img {
  width: 100%;
  display: block;
  transition: transform 0.3s ease;
}

.instagram-post:hover img {
  transform: scale(1.05);
}

.instagram-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1.5rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.instagram-post:hover .instagram-overlay {
  opacity: 1;
}

.instagram-likes, .instagram-comments {
  font-size: 1.2rem;
  font-weight: bold;
}

.instagram-follow-btn {
  display: inline-block;
  margin-top: 2rem;
  padding: 0.8rem 2rem;
  background-color: #d35400;
  color: #fff;
  text-decoration: none;
  font-size: 1.1rem;
  border-radius: 30px;
  transition: background-color 0.3s ease;
}

.instagram-follow-btn:hover {
  background-color: #e67e22;
}

/* Instagram Reels Grid */
.embed-reels {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  max-width: 1200px;
  margin: 2rem auto 0;
  justify-items: center;
}

.instagram-media {
  background: #FFF;
  border: 0;
  border-radius: 12px;
  box-shadow: 0 8px 16px rgba(0,0,0,0.1);
  margin: 0 !important; /* Override default margin */
  max-width: 320px !important; /* Control max width */
  width: 100% !important; /* Ensure it fills the grid cell */
  min-width: 280px !important; /* Prevent it from becoming too small */
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.instagram-media:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 24px rgba(0,0,0,0.15);
}

/* Floating CTA Buttons */
.floating-cta {
  position: fixed;
  bottom: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 1001;
}

.floating-cta .cta-btn {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  border-radius: 25px;
  text-decoration: none;
  color: white;
  font-weight: 500;
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
  transition: transform 0.3s ease;
}

.floating-cta .cta-btn:hover {
  transform: scale(1.05);
}

.floating-cta .cta-btn img {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

.floating-cta .swiggy {
  background-color: #FC8019;
  text-align: center;
}

.floating-cta .zomato {
  background-color: #E23744;
  justify-content: center;
  text-align: center;
}

/* Media Queries for Responsive Design */

/* Tablets and smaller desktops */
@media (max-width: 1024px) {
  .gallery-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

/* Mobile devices */
@media (max-width: 768px) {
  .navbar {
    flex-direction: column;
    padding: 1rem;
  }

  .nav-links {
    margin-top: 1rem;
    gap: 1rem;
  }

  .hero-content h1 {
    font-size: 2.5rem;
  }

  .tagline {
    font-size: 1rem;
  }

  .gallery-grid {
    grid-template-columns: 1fr;
  }

  .reservation-container {
    flex-direction: column;
  }

  .review-container {
    flex-direction: column;
    align-items: center;
  }

  .embed-reels {
    grid-template-columns: 1fr;
  }

  .floating-cta {
    bottom: 10px;
    right: 10px;
  }
}

/* Smaller mobile devices */
@media (max-width: 480px) {
  .nav-logo {
    font-size: 1.2rem;
  }

  .nav-links a {
    font-size: 0.9rem;
  }

  .hero-content h1 {
    font-size: 2rem;
  }

  .tagline {
    font-size: 0.9rem;
  }

  .hero-btn {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
  }

  #menu h2 {
    font-size: 2rem;
  }

  .menu-toggle {
    font-size: 1rem;
  }

  .floating-cta .cta-btn {
    padding: 8px 12px;
  }

  .floating-cta .cta-btn span {
    display: none;
  }

  .floating-cta .cta-btn img {
    margin-right: 0;
  }
}