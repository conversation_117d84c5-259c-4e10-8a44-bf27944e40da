document.addEventListener('DOMContentLoaded', () => {
  // Notification Bar Logic
  const notificationBar = document.getElementById('notification-bar');
  const closeNotification = document.getElementById('close-notification');

  const now = new Date();
  const currentHour = now.getHours();

  // Show between 12 PM (12) and 11 PM (23)
  if (currentHour >= 12 && currentHour < 23) {
    notificationBar.style.display = 'block';
  }

  closeNotification.addEventListener('click', () => {
    notificationBar.style.display = 'none';
  });

  // Navbar scroll effect
  const navbar = document.querySelector('.navbar');
  window.addEventListener('scroll', () => {
    if (window.scrollY > 50) {
      navbar.classList.add('scrolled');
    } else {
      navbar.classList.remove('scrolled');
    }
  });

  // Menu toggle functionality with smooth slide
  const menuToggles = document.querySelectorAll('.menu-toggle');
  menuToggles.forEach(toggle => {
    toggle.addEventListener('click', () => {
      const menuItems = toggle.nextElementSibling;
      const icon = toggle.querySelector('.toggle-icon');

      if (menuItems.style.maxHeight) {
        menuItems.style.maxHeight = null;
        toggle.classList.remove('active');
      } else {
        menuItems.style.maxHeight = menuItems.scrollHeight + 'px';
        toggle.classList.add('active');
      }
    });
  });

  // Lightbox functionality
  const lightboxLinks = document.querySelectorAll('.lightbox');
  const lightboxOverlay = document.createElement('div');
  lightboxOverlay.id = 'lightbox-overlay';
  document.body.appendChild(lightboxOverlay);

  lightboxLinks.forEach(link => {
    link.addEventListener('click', e => {
      e.preventDefault();
      const img = document.createElement('img');
      img.src = link.href;
      lightboxOverlay.innerHTML = '';
      lightboxOverlay.appendChild(img);
      lightboxOverlay.style.display = 'flex';
    });
  });

  lightboxOverlay.addEventListener('click', () => {
    lightboxOverlay.style.display = 'none';
  });

  // Reservation form with enhanced validation
  const reservationForm = document.querySelector('.reservation-form');
  if (reservationForm) {
    reservationForm.addEventListener('submit', function (e) {
      e.preventDefault();

      const name = document.getElementById('name').value.trim();
      const date = document.getElementById('date').value;
      const time = document.getElementById('time').value;
      const guests = document.getElementById('guests').value;
      const phone = document.getElementById('phone').value.trim();

      if (!name || !date || !time || !guests || !phone) {
        alert('Please fill out all fields.');
        return;
      }

      if (!/^\d{10}$/.test(phone)) {
        alert('Please enter a valid 10-digit phone number.');
        return;
      }

      fetch('/reserve', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name, date, time, guests, phone }),
      })
      .then(response => response.json())
      .then(data => {
        if (data.error) {
          alert(`Error: ${data.error}`);
        } else {
          alert('🎉 Thank you! Your table has been reserved. We look forward to serving you.');
          reservationForm.reset();
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('There was an error submitting your reservation. Please try again.');
      });
    });
  }

  // QR Code Generator
  const generateQRBtn = document.getElementById('generate-qr-btn');
  const qrCodeContainer = document.querySelector('.qr-code-container');

  if (generateQRBtn) {
    generateQRBtn.addEventListener('click', () => {
      const qr = qrcode(0, 'L');
      const url = window.location.href;
      qr.addData(url);
      qr.make();
      qrCodeContainer.innerHTML = qr.createImgTag(6, 10);
      generateQRBtn.style.display = 'none';
    });
  }

  // Scroll to Top Button
  const scrollToTopBtn = document.createElement('button');
  scrollToTopBtn.innerHTML = '&uarr;';
  scrollToTopBtn.id = 'scroll-to-top';
  document.body.appendChild(scrollToTopBtn);

  window.addEventListener('scroll', () => {
    if (window.pageYOffset > 300) {
      scrollToTopBtn.style.display = 'block';
    } else {
      scrollToTopBtn.style.display = 'none';
    }
  });

  scrollToTopBtn.addEventListener('click', () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  });
});
