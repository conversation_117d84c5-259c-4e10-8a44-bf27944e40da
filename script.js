document.addEventListener('DOMContentLoaded', () => {
  // Notification Bar Logic
  const notificationBar = document.getElementById('notification-bar');
  const closeNotification = document.getElementById('close-notification');

  const now = new Date();
  const currentHour = now.getHours();

  // Show between 12 PM (12) and 11 PM (23)
  if (currentHour >= 12 && currentHour < 23) {
    notificationBar.style.display = 'block';
  }

  closeNotification.addEventListener('click', () => {
    notificationBar.style.display = 'none';
  });

  // Menu toggle functionality
  const menuToggles = document.querySelectorAll('.menu-toggle');
  menuToggles.forEach(toggle => {
    toggle.addEventListener('click', () => {
      const menuCategory = toggle.parentElement;
      menuCategory.classList.toggle('active');
    });
  });

  // Lightbox functionality
  const lightboxLinks = document.querySelectorAll('.lightbox');
  const lightboxOverlay = document.createElement('div');
  lightboxOverlay.id = 'lightbox-overlay';
  document.body.appendChild(lightboxOverlay);

  lightboxLinks.forEach(link => {
    link.addEventListener('click', e => {
      e.preventDefault();
      const img = document.createElement('img');
      img.src = link.href;
      lightboxOverlay.innerHTML = '';
      lightboxOverlay.appendChild(img);
      lightboxOverlay.style.display = 'flex';
    });
  });

  lightboxOverlay.addEventListener('click', () => {
    lightboxOverlay.style.display = 'none';
  });

  // Reservation form
  const reservationForm = document.querySelector('.reservation-form');
  if (reservationForm) {
    reservationForm.addEventListener('submit', function (e) {
      e.preventDefault();

      const name = document.getElementById('name').value;
      const date = document.getElementById('date').value;
      const time = document.getElementById('time').value;
      const guests = document.getElementById('guests').value;
      const phone = document.getElementById('phone').value;

      // Basic validation
      if (!name || !date || !time || !guests || !phone) {
        alert('Please fill out all fields.');
        return;
      }

      fetch('/reserve', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name, date, time, guests, phone }),
      })
      .then(response => response.json())
      .then(data => {
        if (data.error) {
          alert(`Error: ${data.error}`);
        } else {
          alert('🎉 Thank you! Your table has been reserved. We look forward to serving you.');
          reservationForm.reset();
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('There was an error submitting your reservation. Please try again.');
      });
    });
  }
});
